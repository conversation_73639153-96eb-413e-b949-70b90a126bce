"use client"
import React from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Settings, LogOut, Search } from "lucide-react"
import { useRouter } from "next/navigation"

const index = () => {
    const router = useRouter();
    const deleteCookie = (name: string) => {
        document.cookie = `${name}=; Max-Age=0; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    };

    const handleLogout = () => {
        deleteCookie("auth-token");
        deleteCookie("user-type");
        // localStorage.clear();
        router.replace("/");
    };

    return (
        <div className="sticky top-0 z-10 shadow-sm" >
            <div className="container mx-auto px-4 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <h1 className="text-xl font-bold text-gray-900 sm:text-2xl">Dashboard</h1>
                        <Badge variant="outline" className="hidden sm:flex">
                            Student
                        </Badge>
                    </div>
                    <div className="flex items-center gap-3">
                        <div className="relative hidden md:block">
                            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search resources..."
                                className="h-10 rounded-full border border-gray-200 bg-gray-50 pl-10 pr-4 text-sm focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
                            />
                        </div>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="icon" className="rounded-full">
                                    <Settings className="h-5 w-5" />
                                </Button>
                            </DropdownMenuTrigger>
                            {/* <DropdownMenuTrigger asChild>
                           <Button variant="ghost" size="icon" className="rounded-full">
                               <Avatar>
                                   <AvatarImage src="https://github.com/vercel/next.js/raw/main/packages/next/image/src/next_image.tsx" />
                                   <AvatarFallback>VS</AvatarFallback>
                               </Avatar>
                           </Button>
                       </DropdownMenuTrigger> */}
                            <DropdownMenuContent>
                                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                    Alex Johnson
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem><EMAIL></DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                    <DropdownMenuItem onClick={handleLogout}>Logout</DropdownMenuItem>

                                    {/* <Button variant="outline" size="icon" className="rounded-full" onClick={handleLogout}>
                                   <LogOut className="h-5 w-5" />
                               </Button> */}
                                </DropdownMenuItem>                                </DropdownMenuContent>
                        </DropdownMenu>




                    </div>
                </div>
            </div>
        </div >
    )
}

export default index
