"use client"

import type React from "react"
import { useRouter } from "next/navigation"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Eye, EyeOff } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"

interface AuthModalProps {
    trigger?: React.ReactNode
    defaultTab?: "login" | "signup"
}

export function AuthModal({ trigger, defaultTab = "login" }: AuthModalProps) {
    const router = useRouter()

    const [showPassword, setShowPassword] = useState(false)
    const [open, setOpen] = useState(false)
    const [email, setEmail] = useState("")
    const [applicationType, setApplicationType] = useState<"masters" | "phd" | "athlete" | "degree">("degree");
    const [password, setPassword] = useState("")
    const [userType, setUserType] = useState<"student" | "admin">("student")

    // const setCookie = (name: string, value: string, days: number) => {
    //     const expires = new Date();
    //     expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
    //     document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    // };
    const setCookie = (name: string, value: string, days: number) => {
        const expires = new Date(Date.now() + days * 864e5).toUTCString();
        document.cookie = `${name}=${value}; expires=${expires}; path=/`;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        console.log("Submitting form:", email, password, userType)

        // In a real app, you would validate credentials with your backend here
        if (email && password) {
            // Set authentication cookies that middleware expects
            setCookie("auth-token", "sample-token-value", 7); // 7 days expiry
            setCookie("user-type", userType, 7);

            // Store dummy user data in localStorage
            const userData = {
                name: "Alex Johnson",
                email: email,
                id: "CLA-8220438",
                applicationType: applicationType
            };
            localStorage.setItem("user-data", JSON.stringify(userData));

            // Close modal first to prevent navigation issues
            setOpen(false)

            // Then navigate to the appropriate dashboard
            if (userType === "admin") {
                router.push("/admin")
            } else {
                router.push("/student")
            }
        }
    }
    const toggleUserType = () => {
        setUserType(userType === "student" ? "admin" : "student")
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                {trigger || (
                    <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                        LOGIN
                    </Button>
                )}
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle className="text-center text-2xl font-bold">Welcome to CLA 360</DialogTitle>
                    <DialogDescription className="text-center">Your gateway to top universities worldwide</DialogDescription>
                </DialogHeader>
                <Tabs defaultValue={defaultTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="login">Login</TabsTrigger>
                        <TabsTrigger value="signup">Sign Up</TabsTrigger>
                    </TabsList>
                    <TabsContent value="login">
                        <form onSubmit={handleSubmit} className="space-y-4 py-4">
                            <div className="space-y-2">
                                <Label htmlFor="email-login">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="password-login">Password</Label>
                                <div className="relative">
                                    <Input
                                        id="password-login"
                                        type={showPassword ? "text" : "password"}
                                        placeholder="••••••••"
                                        required
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? (
                                            <EyeOff className="h-4 w-4 text-gray-500" />
                                        ) : (
                                            <Eye className="h-4 w-4 text-gray-500" />
                                        )}
                                    </Button>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="application-type">Application Type</Label>
                                <Select
                                    value={applicationType}
                                    onValueChange={(value) => setApplicationType(value as typeof applicationType)}
                                >
                                    <SelectTrigger id="application-type">
                                        <SelectValue placeholder="Select your application type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="masters">Masters</SelectItem>
                                        <SelectItem value="phd">PhD</SelectItem>
                                        <SelectItem value="athlete">Athlete</SelectItem>
                                        <SelectItem value="degree">Degree</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-center justify-between">
                                {/* <div className="flex items-center space-x-2">
                                    <Input id="remember" type="checkbox" className="h-4 w-4" />
                                    <Label htmlFor="remember" className="text-sm font-normal">
                                        Remember me
                                    </Label>
                                </div> */}
                                <div className="flex items-center space-x-2">
                                    <Input
                                        id="userType"
                                        type="checkbox"
                                        className="h-4 w-4"
                                        checked={userType === "admin"}
                                        onChange={toggleUserType}
                                    />
                                    <Label htmlFor="userType" className="text-sm font-normal">
                                        Login as {userType === "admin" ? "Admin" : "Student"}
                                    </Label>
                                </div>
                                <Button variant="link" className="h-auto p-0 text-sm text-purple-700">
                                    Forgot password?
                                </Button>
                            </div>
                            <Button
                                type="submit"
                                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                            >
                                Login
                            </Button>
                        </form>
                    </TabsContent>
                    <TabsContent value="signup">
                        <form onSubmit={handleSubmit} className="space-y-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="first-name">First Name</Label>
                                    <Input id="first-name" placeholder="John" required />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="last-name">Last Name</Label>
                                    <Input id="last-name" placeholder="Doe" required />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="email-signup">Email</Label>
                                <Input id="email-signup" type="email" placeholder="<EMAIL>" required />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="password-signup">Password</Label>
                                <div className="relative">
                                    <Input
                                        id="password-signup"
                                        type={showPassword ? "text" : "password"}
                                        placeholder="••••••••"
                                        required
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? (
                                            <EyeOff className="h-4 w-4 text-gray-500" />
                                        ) : (
                                            <Eye className="h-4 w-4 text-gray-500" />
                                        )}
                                    </Button>
                                </div>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Input id="terms" type="checkbox" className="h-4 w-4" required />
                                <Label htmlFor="terms" className="text-sm font-normal">
                                    I agree to the{" "}
                                    <Button variant="link" className="h-auto p-0 text-sm text-purple-700">
                                        Terms of Service
                                    </Button>{" "}
                                    and{" "}
                                    <Button variant="link" className="h-auto p-0 text-sm text-purple-700">
                                        Privacy Policy
                                    </Button>
                                </Label>
                            </div>
                            <Button
                                type="submit"
                                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                            >
                                Create Account
                            </Button>
                        </form>
                    </TabsContent>
                </Tabs>
            </DialogContent>
        </Dialog>
    )
}
