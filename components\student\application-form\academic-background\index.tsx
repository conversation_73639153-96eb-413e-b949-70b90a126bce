"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, GraduationCap, School } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface FormData {
    highSchoolName: string
    highSchoolGraduationDate: Date | undefined
    isUniversityGraduate: boolean
    universityName: string
    universityCourse: string
    universityQualification: string
    universityGraduationDate: Date | undefined
    universityGPA: string
}

export default function AcademicBackgroundPage() {
    const router = useRouter()
    const [formData, setFormData] = useState<FormData>({
        highSchoolName: "",
        highSchoolGraduationDate: undefined,
        isUniversityGraduate: false,
        universityName: "",
        universityCourse: "",
        universityQualification: "",
        universityGraduationDate: undefined,
        universityGPA: "",
    })
    const [errors, setErrors] = useState<Record<string, string>>({})
    const [isLoading, setIsLoading] = useState(false)

    useEffect(() => {
        const saved = localStorage.getItem("academicBackground_data")
        if (saved) {
            const parsedData = JSON.parse(saved)
            setFormData({
                ...parsedData,
                highSchoolGraduationDate: parsedData.highSchoolGraduationDate
                    ? new Date(parsedData.highSchoolGraduationDate)
                    : undefined,
                universityGraduationDate: parsedData.universityGraduationDate
                    ? new Date(parsedData.universityGraduationDate)
                    : undefined,
            })
        }
    }, [])

    const validateForm = () => {
        const newErrors: Record<string, string> = {}

        if (!formData.highSchoolName.trim()) {
            newErrors.highSchoolName = "High school name is required"
        }
        if (!formData.highSchoolGraduationDate) {
            newErrors.highSchoolGraduationDate = "High school graduation date is required"
        }

        if (formData.isUniversityGraduate) {
            if (!formData.universityName.trim()) {
                newErrors.universityName = "University name is required"
            }
            if (!formData.universityCourse.trim()) {
                newErrors.universityCourse = "Course is required"
            }
            if (!formData.universityQualification) {
                newErrors.universityQualification = "Qualification is required"
            }
            if (!formData.universityGraduationDate) {
                newErrors.universityGraduationDate = "University graduation date is required"
            }
            if (!formData.universityGPA.trim()) {
                newErrors.universityGPA = "GPA/Grade is required"
            }
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!validateForm()) return

        setIsLoading(true)

        // Save to localStorage
        localStorage.setItem("academicBackground_data", JSON.stringify(formData))
        localStorage.setItem("academicBackground_completed", "true")

        // Dispatch event to update sidebar
        window.dispatchEvent(new Event("formStatusUpdate"))

        await new Promise((resolve) => setTimeout(resolve, 1000))
        setIsLoading(false)

        router.push("/student/application-form/program-preference")
    }

    const handlePrevious = () => {
        localStorage.setItem("academicBackground_data", JSON.stringify(formData))
        router.push("/student/application-form/guardian-info")
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            <div className="container mx-auto px-4 py-8">
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
                    <Card className="max-w-6xl mx-auto border-none shadow-xl bg-white/80 backdrop-blur-sm">

                        <CardHeader className="text-center pb-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm"></div>
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                                className="relative mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-6 backdrop-blur-sm"
                            >
                                <GraduationCap className="w-8 h-8 text-white" />
                            </motion.div>
                            <CardTitle className="text-4xl font-bold relative"> Academic Background</CardTitle>
                            <p className="text-purple-100 mt-2 relative">
                                Please provide your academic background information.
                            </p>
                        </CardHeader>

                        <CardContent className="space-y-8 pt-2">
                            <form onSubmit={handleSubmit} className="space-y-8">
                                {/* High School Section */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                    className="space-y-6"
                                >
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <School className="w-6 h-6 text-blue-600" />
                                        <h3 className="text-xl font-semibold text-gray-800">High School</h3>
                                    </div>

                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="highSchoolName" className="text-sm font-medium text-gray-700">
                                                Name *
                                            </Label>
                                            <Input
                                                id="highSchoolName"
                                                value={formData.highSchoolName}
                                                onChange={(e) => setFormData((prev) => ({ ...prev, highSchoolName: e.target.value }))}
                                                className={cn(
                                                    "h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.highSchoolName && "border-red-500",
                                                )}
                                                placeholder="Enter your high school name"
                                            />
                                            {errors.highSchoolName && <p className="text-sm text-red-600">{errors.highSchoolName}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium text-gray-700">Graduation Date *</Label>
                                            <Popover>
                                                <PopoverTrigger asChild>
                                                    <Button
                                                        variant="outline"
                                                        className={cn(
                                                            "w-full h-12 justify-start text-left font-normal border-gray-200",
                                                            !formData.highSchoolGraduationDate && "text-muted-foreground",
                                                            errors.highSchoolGraduationDate && "border-red-500",
                                                        )}
                                                    >
                                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                                        {formData.highSchoolGraduationDate ? (
                                                            format(formData.highSchoolGraduationDate, "PPP")
                                                        ) : (
                                                            <span>Select date</span>
                                                        )}
                                                    </Button>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-auto p-0" align="start">
                                                    <Calendar
                                                        mode="single"
                                                        selected={formData.highSchoolGraduationDate}
                                                        onSelect={(date) => setFormData((prev) => ({ ...prev, highSchoolGraduationDate: date }))}
                                                        initialFocus
                                                    />
                                                </PopoverContent>
                                            </Popover>
                                            {errors.highSchoolGraduationDate && (
                                                <p className="text-sm text-red-600">{errors.highSchoolGraduationDate}</p>
                                            )}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* University Graduate Toggle */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                    className="flex items-center justify-center space-x-3 py-6"
                                >
                                    <Switch
                                        id="university-graduate"
                                        checked={formData.isUniversityGraduate}
                                        onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, isUniversityGraduate: checked }))}
                                        className="data-[state=checked]:bg-blue-600"
                                    />
                                    <Label htmlFor="university-graduate" className="text-lg font-medium text-gray-700">
                                        University graduate
                                    </Label>
                                </motion.div>

                                {/* Previous University Section */}
                                {formData.isUniversityGraduate && (
                                    <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: "auto" }}
                                        exit={{ opacity: 0, height: 0 }}
                                        transition={{ duration: 0.3 }}
                                        className="space-y-6"
                                    >
                                        <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                            <GraduationCap className="w-6 h-6 text-purple-600" />
                                            <h3 className="text-xl font-semibold text-gray-800">Previous university</h3>
                                        </div>

                                        <div className="grid md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="universityName" className="text-sm font-medium text-gray-700">
                                                    Name *
                                                </Label>
                                                <Input
                                                    id="universityName"
                                                    value={formData.universityName}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, universityName: e.target.value }))}
                                                    className={cn(
                                                        "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.universityName && "border-red-500",
                                                    )}
                                                    placeholder="Enter university name"
                                                />
                                                {errors.universityName && <p className="text-sm text-red-600">{errors.universityName}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="universityCourse" className="text-sm font-medium text-gray-700">
                                                    Course *
                                                </Label>
                                                <Input
                                                    id="universityCourse"
                                                    value={formData.universityCourse}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, universityCourse: e.target.value }))}
                                                    className={cn(
                                                        "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.universityCourse && "border-red-500",
                                                    )}
                                                    placeholder="Enter course name"
                                                />
                                                {errors.universityCourse && <p className="text-sm text-red-600">{errors.universityCourse}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label className="text-sm font-medium text-gray-700">Qualification *</Label>
                                                <Select
                                                    value={formData.universityQualification}
                                                    onValueChange={(value) =>
                                                        setFormData((prev) => ({ ...prev, universityQualification: value }))
                                                    }
                                                >
                                                    <SelectTrigger
                                                        className={cn(
                                                            "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                            errors.universityQualification && "border-red-500",
                                                        )}
                                                    >
                                                        <SelectValue placeholder="Choose qualification" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="bachelor">Bachelor's Degree</SelectItem>
                                                        <SelectItem value="master">Master's Degree</SelectItem>
                                                        <SelectItem value="phd">PhD</SelectItem>
                                                        <SelectItem value="diploma">Diploma</SelectItem>
                                                        <SelectItem value="certificate">Certificate</SelectItem>
                                                        <SelectItem value="associate">Associate Degree</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors.universityQualification && (
                                                    <p className="text-sm text-red-600">{errors.universityQualification}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label className="text-sm font-medium text-gray-700">Graduation Date *</Label>
                                                <Popover>
                                                    <PopoverTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            className={cn(
                                                                "w-full h-12 justify-start text-left font-normal border-gray-200",
                                                                !formData.universityGraduationDate && "text-muted-foreground",
                                                                errors.universityGraduationDate && "border-red-500",
                                                            )}
                                                        >
                                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                                            {formData.universityGraduationDate ? (
                                                                format(formData.universityGraduationDate, "PPP")
                                                            ) : (
                                                                <span>Select date</span>
                                                            )}
                                                        </Button>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-auto p-0" align="start">
                                                        <Calendar
                                                            mode="single"
                                                            selected={formData.universityGraduationDate}
                                                            onSelect={(date) => setFormData((prev) => ({ ...prev, universityGraduationDate: date }))}
                                                            initialFocus
                                                        />
                                                    </PopoverContent>
                                                </Popover>
                                                {errors.universityGraduationDate && (
                                                    <p className="text-sm text-red-600">{errors.universityGraduationDate}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2 md:col-span-2">
                                                <Label htmlFor="universityGPA" className="text-sm font-medium text-gray-700">
                                                    GPA / Grade (e.g., 3.8/4.0, or 4.3/5.0) *
                                                </Label>
                                                <Input
                                                    id="universityGPA"
                                                    value={formData.universityGPA}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, universityGPA: e.target.value }))}
                                                    className={cn(
                                                        "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.universityGPA && "border-red-500",
                                                    )}
                                                    placeholder="Enter your GPA or grade"
                                                />
                                                {errors.universityGPA && <p className="text-sm text-red-600">{errors.universityGPA}</p>}
                                            </div>
                                        </div>
                                    </motion.div>
                                )}

                                {/* Navigation Buttons */}
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.3 }}
                                    className="flex gap-4 pt-8"
                                >
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handlePrevious}
                                        className="flex-1 h-12 text-gray-600 border-gray-300 hover:bg-gray-50"
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                                    >
                                        {isLoading ? "Saving..." : "Save"}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </div>
    )
}
