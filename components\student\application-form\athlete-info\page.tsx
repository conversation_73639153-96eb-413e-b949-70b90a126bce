"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Trophy, Plus, X, User, Ruler, Zap, Video, Award, CheckCircle, Loader2, Target } from "lucide-react"

interface StrongSuit {
    id: string
    skill: string
}

interface VideoPortfolio {
    id: string
    title: string
    url: string
}

interface Achievement {
    id: string
    name: string
    date: string
}

export default function AthleteInfoPage() {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)

    // Form state
    const [majorSport, setMajorSport] = useState("")
    const [position, setPosition] = useState("")
    const [team, setTeam] = useState("")
    const [strongSuits, setStrongSuits] = useState<StrongSuit[]>([])
    const [newStrongSuit, setNewStrongSuit] = useState("")

    // Coach Reference
    const [coachName, setCoachName] = useState("")
    const [coachContact, setCoachContact] = useState("")
    const [coachEmail, setCoachEmail] = useState("")

    // Physical Stats
    const [height, setHeight] = useState("")
    const [weight, setWeight] = useState("")
    const [averageSpeed, setAverageSpeed] = useState("")

    // Video Portfolio
    const [videoPortfolios, setVideoPortfolios] = useState<VideoPortfolio[]>([])
    const [newVideoTitle, setNewVideoTitle] = useState("")
    const [newVideoUrl, setNewVideoUrl] = useState("")

    // Athletic Achievements
    const [achievements, setAchievements] = useState<Achievement[]>([])
    const [newAchievementName, setNewAchievementName] = useState("")
    const [newAchievementDate, setNewAchievementDate] = useState("")

    // Load saved data on component mount
    useEffect(() => {
        const savedData = localStorage.getItem("athleteInfo_data")
        if (savedData) {
            const data = JSON.parse(savedData)
            setMajorSport(data.majorSport || "")
            setPosition(data.position || "")
            setTeam(data.team || "")
            setStrongSuits(data.strongSuits || [])
            setCoachName(data.coachName || "")
            setCoachContact(data.coachContact || "")
            setCoachEmail(data.coachEmail || "")
            setHeight(data.height || "")
            setWeight(data.weight || "")
            setAverageSpeed(data.averageSpeed || "")
            setVideoPortfolios(data.videoPortfolios || [])
            setAchievements(data.achievements || [])
        }
    }, [])

    // Save data to localStorage
    const saveData = () => {
        const data = {
            majorSport,
            position,
            team,
            strongSuits,
            coachName,
            coachContact,
            coachEmail,
            height,
            weight,
            averageSpeed,
            videoPortfolios,
            achievements,
        }
        localStorage.setItem("athleteInfo_data", JSON.stringify(data))
    }

    // Strong Suits functions
    const addStrongSuit = () => {
        if (newStrongSuit.trim()) {
            const suit: StrongSuit = {
                id: Date.now().toString(),
                skill: newStrongSuit.trim(),
            }
            setStrongSuits([...strongSuits, suit])
            setNewStrongSuit("")
        }
    }

    const removeStrongSuit = (id: string) => {
        setStrongSuits(strongSuits.filter((suit) => suit.id !== id))
    }

    // Video Portfolio functions
    const addVideoPortfolio = () => {
        if (newVideoTitle.trim() && newVideoUrl.trim()) {
            const video: VideoPortfolio = {
                id: Date.now().toString(),
                title: newVideoTitle.trim(),
                url: newVideoUrl.trim(),
            }
            setVideoPortfolios([...videoPortfolios, video])
            setNewVideoTitle("")
            setNewVideoUrl("")
        }
    }

    const removeVideoPortfolio = (id: string) => {
        setVideoPortfolios(videoPortfolios.filter((video) => video.id !== id))
    }

    // Achievement functions
    const addAchievement = () => {
        if (newAchievementName.trim() && newAchievementDate.trim()) {
            const achievement: Achievement = {
                id: Date.now().toString(),
                name: newAchievementName.trim(),
                date: newAchievementDate.trim(),
            }
            setAchievements([...achievements, achievement])
            setNewAchievementName("")
            setNewAchievementDate("")
        }
    }

    const removeAchievement = (id: string) => {
        setAchievements(achievements.filter((achievement) => achievement.id !== id))
    }

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)

        // Save data
        saveData()

        // Mark as completed
        localStorage.setItem("athleteInfo_completed", "true")

        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500))

        setIsLoading(false)
        setShowSuccess(true)

        // Dispatch custom event to update sidebar
        window.dispatchEvent(new Event("formStatusUpdate"))

        // Auto-hide success message and redirect
        setTimeout(() => {
            setShowSuccess(false)
            // Show success message for final application submission
            alert("Application submitted successfully!")
            router.push("/student")
        }, 2000)
    }

    // Handle save (without completion)
    const handleSave = () => {
        saveData()
        // Show brief save confirmation
        const originalText = "Save"
        const saveButton = document.querySelector("[data-save-button]")
        if (saveButton) {
            saveButton.textContent = "Saved!"
            setTimeout(() => {
                saveButton.textContent = originalText
            }, 1000)
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50">
            {/* Success Overlay */}
            <AnimatePresence>
                {showSuccess && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm"
                    >
                        <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.8, opacity: 0 }}
                            className="bg-white rounded-2xl p-8 shadow-2xl border border-green-200"
                        >
                            <div className="text-center">
                                <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                                    className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center"
                                >
                                    <CheckCircle className="w-8 h-8 text-green-600" />
                                </motion.div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">Athlete Info Completed!</h3>
                                <p className="text-gray-600">Your athletic information has been saved successfully.</p>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
            <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                {/* <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-600 to-red-600 rounded-2xl mb-4 shadow-lg">
                            <Trophy className="w-8 h-8 text-white" />
                        </div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2">
                            Athlete Information
                        </h1>
                        <p className="text-gray-600 max-w-2xl mx-auto">
                            Share your athletic background, achievements, and performance statistics to showcase your sporting
                            excellence.
                        </p>
                    </motion.div> */}

                <Card className="border-none shadow-xl bg-white/80 backdrop-blur-sm">
                    <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg text-center">
                        <CardTitle className="flex flex-col items-center justify-center gap-3 text-xl">
                            <div className="p-2 bg-white/20 rounded-lg">
                                <Trophy className="w-8 h-8 text-white" />
                            </div>
                            Athlete Information
                        </CardTitle>
                        <CardDescription className="text-blue-100">
                            Share your athletic background, achievements, and performance statistics to showcase your sporting
                            excellence.
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="p-6 sm:p-8">
                        <form onSubmit={handleSubmit} className="space-y-8">
                            {/* Basic Athletic Info */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-orange-50 to-orange-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-orange-800">
                                            <Target className="w-6 h-6" />
                                            Athletic Background
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="majorSport" className="text-orange-700 font-medium">
                                                Major Sport
                                            </Label>
                                            <Input
                                                id="majorSport"
                                                value={majorSport}
                                                onChange={(e) => setMajorSport(e.target.value)}
                                                placeholder="e.g., Basketball, Soccer, Swimming"
                                                className="h-12 border-orange-200 focus:border-orange-400"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="position" className="text-orange-700 font-medium">
                                                Position
                                            </Label>
                                            <Input
                                                id="position"
                                                value={position}
                                                onChange={(e) => setPosition(e.target.value)}
                                                placeholder="e.g., Point Guard, Midfielder"
                                                className="h-12 border-orange-200 focus:border-orange-400"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="team" className="text-orange-700 font-medium">
                                                Team
                                            </Label>
                                            <Input
                                                id="team"
                                                value={team}
                                                onChange={(e) => setTeam(e.target.value)}
                                                placeholder="e.g., Team A, Team B"
                                                className="h-12 border-orange-200 focus:border-orange-400"
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Strong Suits */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-blue-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-blue-800">
                                            <Zap className="w-6 h-6" />
                                            Strong Suits
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex gap-3">
                                            <Input
                                                value={newStrongSuit}
                                                onChange={(e) => setNewStrongSuit(e.target.value)}
                                                placeholder="e.g., Speed, Agility, Leadership, Endurance"
                                                className="flex-1 h-12 border-blue-200 focus:border-blue-400"
                                                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addStrongSuit())}
                                            />
                                            <Button
                                                type="button"
                                                onClick={addStrongSuit}
                                                className="h-12 px-6 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                                            >
                                                <Plus className="w-4 h-4 mr-2" />
                                                Add
                                            </Button>
                                        </div>

                                        <AnimatePresence>
                                            {strongSuits.map((suit) => (
                                                <motion.div
                                                    key={suit.id}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    exit={{ opacity: 0, x: 20 }}
                                                    className="flex items-center justify-between bg-white p-4 rounded-lg border border-blue-200"
                                                >
                                                    <span className="text-gray-800">{suit.skill}</span>
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => removeStrongSuit(suit.id)}
                                                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                                    >
                                                        <X className="w-4 h-4" />
                                                    </Button>
                                                </motion.div>
                                            ))}
                                        </AnimatePresence>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Coach Reference */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-green-50 to-green-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-green-800">
                                            <User className="w-6 h-6" />
                                            Coach Reference
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="coachName" className="text-green-700 font-medium">
                                                Name
                                            </Label>
                                            <Input
                                                id="coachName"
                                                value={coachName}
                                                onChange={(e) => setCoachName(e.target.value)}
                                                placeholder="Coach's full name"
                                                className="h-12 border-green-200 focus:border-green-400"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="coachContact" className="text-green-700 font-medium">
                                                Contact
                                            </Label>
                                            <Input
                                                id="coachContact"
                                                value={coachContact}
                                                onChange={(e) => setCoachContact(e.target.value)}
                                                placeholder="Phone number"
                                                className="h-12 border-green-200 focus:border-green-400"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="coachEmail" className="text-green-700 font-medium">
                                                Email
                                            </Label>
                                            <Input
                                                id="coachEmail"
                                                type="email"
                                                value={coachEmail}
                                                onChange={(e) => setCoachEmail(e.target.value)}
                                                placeholder="<EMAIL>"
                                                className="h-12 border-green-200 focus:border-green-400"
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Physical Stats */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-purple-50 to-purple-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-purple-800">
                                            <Ruler className="w-6 h-6" />
                                            Physical Stats
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="height" className="text-purple-700 font-medium">
                                                Height
                                            </Label>
                                            <Input
                                                id="height"
                                                value={height}
                                                onChange={(e) => setHeight(e.target.value)}
                                                placeholder="e.g., 6'2&quot; or 188 cm"
                                                className="h-12 border-purple-200 focus:border-purple-400"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="weight" className="text-purple-700 font-medium">
                                                Weight
                                            </Label>
                                            <Input
                                                id="weight"
                                                value={weight}
                                                onChange={(e) => setWeight(e.target.value)}
                                                placeholder="e.g., 180 lbs or 82 kg"
                                                className="h-12 border-purple-200 focus:border-purple-400"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="averageSpeed" className="text-purple-700 font-medium">
                                                Average Speed
                                            </Label>
                                            <Input
                                                id="averageSpeed"
                                                value={averageSpeed}
                                                onChange={(e) => setAverageSpeed(e.target.value)}
                                                placeholder="e.g., 25 mph or 40 km/h"
                                                className="h-12 border-purple-200 focus:border-purple-400"
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Video Portfolio */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-red-50 to-red-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-red-800">
                                            <Video className="w-6 h-6" />
                                            Video Portfolio
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <Input
                                                value={newVideoTitle}
                                                onChange={(e) => setNewVideoTitle(e.target.value)}
                                                placeholder="Video title"
                                                className="h-12 border-red-200 focus:border-red-400"
                                            />
                                            <Input
                                                value={newVideoUrl}
                                                onChange={(e) => setNewVideoUrl(e.target.value)}
                                                placeholder="Video URL"
                                                className="h-12 border-red-200 focus:border-red-400"
                                            />
                                        </div>
                                        <Button
                                            type="button"
                                            onClick={addVideoPortfolio}
                                            className="w-full h-12 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"
                                        >
                                            <Plus className="w-4 h-4 mr-2" />
                                            Add Video
                                        </Button>

                                        <AnimatePresence>
                                            {videoPortfolios.map((video) => (
                                                <motion.div
                                                    key={video.id}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    exit={{ opacity: 0, x: 20 }}
                                                    className="bg-white p-4 rounded-lg border border-red-200"
                                                >
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex-1 pr-4">
                                                            <h4 className="font-medium text-gray-800">{video.title}</h4>
                                                            <p className="text-sm text-gray-600 break-all">{video.url}</p>
                                                        </div>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeVideoPortfolio(video.id)}
                                                            className="text-red-500 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                                                        >
                                                            <X className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </AnimatePresence>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Athletic Achievements */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-yellow-50 to-yellow-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-yellow-800">
                                            <Award className="w-6 h-6" />
                                            Athletic Achievements
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <Input
                                                value={newAchievementName}
                                                onChange={(e) => setNewAchievementName(e.target.value)}
                                                placeholder="Achievement name"
                                                className="h-12 border-yellow-200 focus:border-yellow-400"
                                            />
                                            <Input
                                                type="date"
                                                value={newAchievementDate}
                                                onChange={(e) => setNewAchievementDate(e.target.value)}
                                                className="h-12 border-yellow-200 focus:border-yellow-400"
                                            />
                                        </div>
                                        <Button
                                            type="button"
                                            onClick={addAchievement}
                                            className="w-full h-12 bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800"
                                        >
                                            <Plus className="w-4 h-4 mr-2" />
                                            Add Achievement
                                        </Button>

                                        <AnimatePresence>
                                            {achievements.map((achievement) => (
                                                <motion.div
                                                    key={achievement.id}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    exit={{ opacity: 0, x: 20 }}
                                                    className="bg-white p-4 rounded-lg border border-yellow-200"
                                                >
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex-1 pr-4">
                                                            <h4 className="font-medium text-gray-800">{achievement.name}</h4>
                                                            <p className="text-sm text-gray-600">{achievement.date}</p>
                                                        </div>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeAchievement(achievement.id)}
                                                            className="text-red-500 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                                                        >
                                                            <X className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </AnimatePresence>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Navigation Buttons */}
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.7 }}
                                className="flex flex-col sm:flex-row gap-4 pt-6"
                            >
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => router.back()}
                                    className="flex-1 h-12 border-gray-300 hover:bg-gray-50"
                                >
                                    Previous
                                </Button>
                                <Button
                                    type="button"
                                    onClick={handleSave}
                                    data-save-button
                                    className="flex-1 h-12 bg-gray-600 hover:bg-gray-700 text-white"
                                >
                                    Save
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isLoading}
                                    className="flex-1 h-12 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white"
                                >
                                    {isLoading ? (
                                        <>
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            Submitting...
                                        </>
                                    ) : (
                                        "Submit Application"
                                    )}
                                </Button>
                            </motion.div>
                        </form>
                    </CardContent>
                </Card>

            </div>
        </div>
    )
}
