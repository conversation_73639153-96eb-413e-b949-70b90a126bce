"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
    BookOpen,
    CheckCircle2,
    ChevronRight,
    FileText,
    GraduationCap,
    Home,
    LayoutDashboard,
    MoreHorizontal,
    StampIcon as Passport,
    Plane,
    Plus,
    Search,
    Settings,
    Wallet,
    User,
    LogIn,
    Bell,
    PenLine,
    Calendar,
    BarChart3,
    LogOut,
    ChevronDown,
    ChevronUp,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useRouter } from "next/navigation"

// Animation variants
const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
}

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
        },
    },
}




export default function StudentDashboardComponent() {
    const [isJourneyCollapsed, setIsJourneyCollapsed] = useState(false)

    const router = useRouter()
    const [ref, inView] = useInView({
        triggerOnce: true,
        threshold: 0.1,
    })

    const [activeTab, setActiveTab] = useState("overview")

    // Mock data for the dashboard
    const studentInfo = {
        name: "Alex Johnson",
        id: "CLA-8220438",
        profileImage: "/placeholder.svg",
        completionRate: 65,
        notifications: 5,
    }

    // Quick actions
    const quickActions = [
        { name: "Browse Scholarships", icon: BookOpen, color: "bg-blue-500" },
        { name: "Upload Documents", icon: FileText, color: "bg-emerald-500" },
        { name: "Payment", icon: Wallet, color: "bg-amber-500" },
    ]

    // Recent notifications
    const recentNotifications = [
        {
            id: 1,
            title: "Document Verification Complete",
            description: "Your transcript has been verified successfully.",
            time: "2 hours ago",
            icon: CheckCircle2,
            color: "bg-green-100 text-green-600",
        },
        {
            id: 2,
            title: "New Scholarship Available",
            description: "A new scholarship matching your profile is now available.",
            time: "Yesterday",
            icon: GraduationCap,
            color: "bg-blue-100 text-blue-600",
        },
        {
            id: 3,
            title: "Profile Update Reminder",
            description: "Please complete your profile to access all features.",
            time: "2 days ago",
            icon: FileText,
            color: "bg-amber-100 text-amber-600",
        },
        {
            id: 4,
            title: "New Course Enrolled",
            description: "You have enrolled in a new course.",
            time: "2 weeks ago",
            icon: Plane,
            color: "bg-orange-100 text-orange-600",
        },

    ]

    // Recent activities
    const recentActivities = [
        {
            id: 1,
            title: "Profile Updated",
            description: "You updated your academic information.",
            time: "Today at 10:30 AM",
            icon: User,
        },
        {
            id: 2,
            title: "Document Uploaded",
            description: "You uploaded your high school transcript.",
            time: "Yesterday at 2:15 PM",
            icon: FileText,
        },
        {
            id: 3,
            title: "Logged In",
            description: "You logged in from a new device.",
            time: "May 20, 2025 at 9:45 AM",
            icon: LogIn,
        },
    ]

    // Educational resources
    const educationalResources = [
        {
            title: "Guide to Studying Abroad",
            description: "Essential tips for international students",
            image: "/placeholder.svg",
            category: "Guide",
        },
        {
            title: "Scholarship Application Tips",
            description: "How to write winning scholarship essays",
            image: "/placeholder.svg",
            category: "Tips",
        },
        {
            title: "Student Visa Process",
            description: "Step-by-step guide to obtaining your student visa",
            image: "/placeholder.svg",
            category: "Guide",
        },
    ]

    // Progress steps
    const progressSteps = [
        {
            id: "profile",
            title: "Complete Your Profile",
            description: "Add your academic history, achievements, and preferences",
            completed: true,
            icon: CheckCircle2,
        },
        {
            id: "documents",
            title: "Upload Documents",
            description: "Submit your transcripts, recommendations, and test scores",
            completed: true,
            icon: FileText,
        },
        {
            id: "universities",
            title: "Select Universities",
            description: "Choose universities that match your academic goals",
            completed: true,
            icon: GraduationCap,
        },
        {
            id: "applications",
            title: "Submit Applications",
            description: "Complete and submit your university applications",
            completed: false,
            icon: PenLine,
        },
        {
            id: "interviews",
            title: "Prepare for Interviews",
            description: "Schedule and prepare for university interviews",
            completed: false,
            icon: Calendar,
        },
    ]

    // const deleteCookie = (name: string) => {
    //     document.cookie = `${name}=; Max-Age=0; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    // };

    // const handleLogout = () => {
    //     deleteCookie("auth-token");
    //     deleteCookie("user-type");
    //     router.push("/")
    // };

    return (
        <div className="min-h-screen bg-gray-50 pb-10">


            {/* Main Content */}
            <div className="container mx-auto px-4 py-6">
                {/* Welcome Section */}
                <motion.div
                    ref={ref}
                    initial="hidden"
                    animate={inView ? "visible" : "hidden"}
                    variants={fadeInUp}
                    className="mb-6"
                >
                    <Card className="overflow-hidden border-none bg-gradient-to-r from-purple-500 to-blue-500 shadow-md">
                        <CardContent className="p-6 text-white">
                            <div className="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
                                <div className="flex items-center gap-4">
                                    <div className="relative h-16 w-16 overflow-hidden rounded-full border-4 border-white/20 bg-white/10">
                                        <Image
                                            src="/placeholder.svg"
                                            alt={studentInfo.name}
                                            width={64}
                                            height={64}
                                            className="h-full w-full object-cover"
                                        />
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold">Welcome back, {studentInfo.name}!</h2>
                                        <p className="text-blue-100">CLA ID: {studentInfo.id}</p>
                                    </div>
                                </div>
                                <div className="flex w-full flex-wrap gap-4 md:w-auto">
                                    <div className="rounded-lg bg-white/10 px-4 py-2 backdrop-blur-sm">
                                        <p className="text-sm text-blue-100">Profile Completion</p>
                                        <div className="mt-1 flex items-center gap-2">
                                            <Progress value={studentInfo.completionRate} className="h-2 w-24 bg-white/20" />
                                            <span className="text-sm font-medium">{studentInfo.completionRate}%</span>
                                        </div>
                                    </div>
                                    <div className="rounded-lg bg-white/10 px-4 py-2 backdrop-blur-sm">
                                        <p className="text-sm text-blue-100">Notifications</p>
                                        <p className="text-xl font-bold">{studentInfo.notifications}</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                {/* Navigation Cards */}
                <motion.div
                    initial="hidden"
                    animate={inView ? "visible" : "hidden"}
                    variants={staggerContainer}
                    className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8"
                >
                    {[
                        { title: "Apply", icon: PenLine, color: "bg-blue-500" },
                        {
                            title: "Scholarships",
                            icon: GraduationCap,
                            color: "bg-purple-500",
                        },
                        {
                            title: "Visa Corner",
                            icon: Passport,
                            color: "bg-emerald-500",
                        },
                        {
                            title: "Accommodation",
                            icon: Home,
                            color: "bg-amber-500",
                        },
                        {
                            title: "Travel Hub",
                            icon: Plane,
                            color: "bg-rose-500",
                        },
                        {
                            title: "Finances",
                            icon: Wallet,
                            color: "bg-indigo-500",
                        },
                        {
                            title: "Resources",
                            icon: BookOpen,
                            color: "bg-cyan-500",
                        },
                        {
                            title: "Support",
                            icon: CheckCircle2,
                            color: "bg-teal-500",
                        },
                    ].map((item, index) => (
                        <motion.div key={index} variants={fadeInUp}>
                            <Link
                                href="#"
                                className="flex flex-col items-center rounded-xl border border-gray-100 bg-white p-4 text-center shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"
                            >
                                <div
                                    className={`mb-3 rounded-full ${item.color} p-3 text-white`}
                                >
                                    <item.icon className="h-5 w-5" />
                                </div>
                                <span className="text-sm font-medium">
                                    {item.title}
                                </span>
                            </Link>
                        </motion.div>
                    ))}
                </motion.div>


                <div className="grid gap-6 md:grid-cols-3 my-6">
                    <div className="md:col-span-3">
                        <Card className="border-none shadow-md">
                            <CardHeader className="pb-3">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle>Your Application Journey</CardTitle>
                                        <CardDescription>Track your progress through the application process</CardDescription>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setIsJourneyCollapsed(!isJourneyCollapsed)}
                                        className="h-8 w-8 p-0 hover:bg-gray-100"
                                        aria-label={isJourneyCollapsed ? "Expand application journey" : "Collapse application journey"}
                                    >
                                        {isJourneyCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
                                    </Button>
                                </div>
                            </CardHeader>

                            <motion.div
                                initial={false}
                                animate={{
                                    height: isJourneyCollapsed ? 0 : "auto",
                                    opacity: isJourneyCollapsed ? 0 : 1,
                                }}
                                transition={{
                                    duration: 0.3,
                                    ease: "easeInOut",
                                }}
                                style={{ overflow: "hidden" }}
                            >
                                <CardContent className={isJourneyCollapsed ? "pb-0" : ""}>
                                    <div className="relative mt-8 pb-4">
                                        {/* Progress line */}
                                        <div className="absolute left-6 top-0 h-full w-0.5 -translate-x-1/2 bg-gray-100"></div>

                                        <div className="space-y-12">
                                            {progressSteps.map((step, index) => (
                                                <div key={index} className="relative">
                                                    {/* Step indicator */}
                                                    <div
                                                        className={`absolute left-6 top-0 flex h-12 w-12 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full border-4 border-white ${step.completed ? "bg-green-500 text-white" : "bg-white text-gray-400"
                                                            }`}
                                                    >
                                                        <step.icon className="h-5 w-5" />
                                                    </div>

                                                    <div className="ml-12 pt-1">
                                                        <h3 className={`text-lg font-bold ${step.completed ? "text-gray-900" : "text-gray-600"}`}>
                                                            {step.title}
                                                        </h3>
                                                        <p className="mt-1 text-gray-500">{step.description}</p>

                                                        {step.completed ? (
                                                            <Badge className="mt-2 bg-green-100 text-green-800">Completed</Badge>
                                                        ) : (
                                                            <Button variant="outline" size="sm" className="mt-3">
                                                                {index === progressSteps.findIndex((s) => !s.completed) ? "Start Now" : "Coming Soon"}
                                                            </Button>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </CardContent>
                            </motion.div>
                        </Card>
                    </div>
                </div>
                {/* Dashboard Tabs */}



            </div>
        </div>
    )
}
