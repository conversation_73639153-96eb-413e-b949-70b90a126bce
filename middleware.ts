import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// This is a simplified middleware for demonstration purposes
// In a real application, you would verify JWT tokens or session cookies
export function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname

  // Define public paths that don't require authentication
  const isPublicPath = path === "/"

  // Get the authentication token from the cookies
  // In a real app, this would be a JWT or session cookie
  const token = request.cookies.get("auth-token")?.value

  // Get the user type from the cookies
  // In a real app, this would be extracted from the JWT payload
  const userType = request.cookies.get("user-type")?.value

  // Redirect logic
  if (isPublicPath && token) {
    // If user is authenticated and trying to access public path,
    // redirect to their dashboard based on user type
    return NextResponse.redirect(new URL(userType === "admin" ? "/admin" : "/student", request.url))
  }

  if (!isPublicPath && !token) {
    // If user is not authenticated and trying to access protected path,
    // redirect to login
    return NextResponse.redirect(new URL("/", request.url))
  }

  if (path.startsWith("/admin") && userType !== "admin") {
    // If user is not an admin but trying to access admin routes,
    // redirect to student dashboard
    return NextResponse.redirect(new URL("/student", request.url))
  }

  if (path.startsWith("/student") && userType === "admin") {
    // If user is an admin but trying to access student routes,
    // redirect to admin dashboard
    return NextResponse.redirect(new URL("/admin", request.url))
  }

  return NextResponse.next()
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public assets)
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.png$).*)",
  ],
}
