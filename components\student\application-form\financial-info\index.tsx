"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DollarSign, Users } from "lucide-react"
import { cn } from "@/lib/utils"

interface FormData {
    financingPlan: string
    affordableAmount: string
    sponsorName: string
    sponsorRelation: string
    sponsorEmail: string
    sponsorPhone: string
    sponsorOccupation: string
}

export default function FinancialInfoPage() {
    const router = useRouter()
    const [formData, setFormData] = useState<FormData>({
        financingPlan: "",
        affordableAmount: "",
        sponsorName: "",
        sponsorRelation: "",
        sponsorEmail: "",
        sponsorPhone: "",
        sponsorOccupation: "",
    })
    const [errors, setErrors] = useState<Record<string, string>>({})
    const [isLoading, setIsLoading] = useState(false)

    useEffect(() => {
        const saved = localStorage.getItem("financialInfo_data")
        if (saved) {
            setFormData(JSON.parse(saved))
        }
    }, [])

    const showSponsorInfo = formData.financingPlan && formData.financingPlan !== "Myself Only"

    const validateForm = () => {
        const newErrors: Record<string, string> = {}

        if (!formData.financingPlan) {
            newErrors.financingPlan = "Please select how you plan to finance your education"
        }
        if (!formData.affordableAmount.trim()) {
            newErrors.affordableAmount = "Please enter the amount you can afford"
        }

        if (showSponsorInfo) {
            if (!formData.sponsorName.trim()) {
                newErrors.sponsorName = "Sponsor name is required"
            }
            if (!formData.sponsorRelation) {
                newErrors.sponsorRelation = "Sponsor relation is required"
            }
            if (!formData.sponsorEmail.trim()) {
                newErrors.sponsorEmail = "Sponsor email is required"
            } else if (!/\S+@\S+\.\S+/.test(formData.sponsorEmail)) {
                newErrors.sponsorEmail = "Please enter a valid email address"
            }
            if (!formData.sponsorPhone.trim()) {
                newErrors.sponsorPhone = "Sponsor phone is required"
            }
            if (!formData.sponsorOccupation.trim()) {
                newErrors.sponsorOccupation = "Sponsor occupation is required"
            }
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!validateForm()) return

        setIsLoading(true)

        localStorage.setItem("financialInfo_data", JSON.stringify(formData))
        localStorage.setItem("financialInfo_completed", "true")

        window.dispatchEvent(new Event("formStatusUpdate"))

        await new Promise((resolve) => setTimeout(resolve, 1000))
        setIsLoading(false)

        router.push("/student/application-form/extracurricular-activities")
    }

    const handlePrevious = () => {
        localStorage.setItem("financialInfo_data", JSON.stringify(formData))
        router.push("/student/application-form/program-preference")
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            <div className="container mx-auto px-4 py-8">
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
                    <Card className="max-w-6xl mx-auto border-none shadow-xl bg-white/80 backdrop-blur-sm">

                        <CardHeader className="text-center pb-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm"></div>
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                                className="relative mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-6 backdrop-blur-sm"
                            >
                                  <DollarSign className="w-8 h-8 text-white" />
                            </motion.div>
                            <CardTitle className="text-4xl font-bold relative">                                Financial Info
                            </CardTitle>
                            <p className="text-purple-100 mt-2 relative">
                                Please provide information about your financial situation and any sponsors you have.
                            </p>
                        </CardHeader>

                        <CardContent className="space-y-8 pt-2">
                            <form onSubmit={handleSubmit} className="space-y-8">
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                    className="space-y-6"
                                >
                                    <div className="space-y-2">
                                        <Label className="text-sm font-medium text-gray-700">
                                            How do you plan to finance your education? *
                                        </Label>
                                        <Select
                                            value={formData.financingPlan}
                                            onValueChange={(value) => setFormData((prev) => ({ ...prev, financingPlan: value }))}
                                        >
                                            <SelectTrigger
                                                className={cn(
                                                    "h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.financingPlan && "border-red-500",
                                                )}
                                            >
                                                <SelectValue placeholder="Select option" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="My Mother">My Mother</SelectItem>
                                                <SelectItem value="My Father">My Father</SelectItem>
                                                <SelectItem value="Both Parents">Both Parents</SelectItem>
                                                <SelectItem value="Uncle">Uncle</SelectItem>
                                                <SelectItem value="Siblings">Siblings</SelectItem>
                                                <SelectItem value="Myself Only">Myself Only</SelectItem>
                                                <SelectItem value="Family and Myself">Family and Myself</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.financingPlan && <p className="text-sm text-red-600">{errors.financingPlan}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="affordableAmount" className="text-sm font-medium text-gray-700">
                                            Assuming you don't get any scholarships, how much school fees can you afford (In Dollars) *
                                        </Label>
                                        <Input
                                            id="affordableAmount"
                                            type="number"
                                            value={formData.affordableAmount}
                                            onChange={(e) => setFormData((prev) => ({ ...prev, affordableAmount: e.target.value }))}
                                            className={cn(
                                                "h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                errors.affordableAmount && "border-red-500",
                                            )}
                                            placeholder="Enter amount in USD"
                                        />
                                        {errors.affordableAmount && <p className="text-sm text-red-600">{errors.affordableAmount}</p>}
                                    </div>
                                </motion.div>

                                {/* Sponsor Information Section */}
                                {showSponsorInfo && (
                                    <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: "auto" }}
                                        exit={{ opacity: 0, height: 0 }}
                                        transition={{ duration: 0.3 }}
                                        className="space-y-6"
                                    >
                                        <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                            <Users className="w-6 h-6 text-purple-600" />
                                            <h3 className="text-xl font-semibold text-gray-800">Sponsor Information</h3>
                                        </div>

                                        <div className="grid md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="sponsorName" className="text-sm font-medium text-gray-700">
                                                    Full name *
                                                </Label>
                                                <Input
                                                    id="sponsorName"
                                                    value={formData.sponsorName}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, sponsorName: e.target.value }))}
                                                    className={cn(
                                                        "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.sponsorName && "border-red-500",
                                                    )}
                                                    placeholder="Enter sponsor's full name"
                                                />
                                                {errors.sponsorName && <p className="text-sm text-red-600">{errors.sponsorName}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label className="text-sm font-medium text-gray-700">Relation *</Label>
                                                <Select
                                                    value={formData.sponsorRelation}
                                                    onValueChange={(value) => setFormData((prev) => ({ ...prev, sponsorRelation: value }))}
                                                >
                                                    <SelectTrigger
                                                        className={cn(
                                                            "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                            errors.sponsorRelation && "border-red-500",
                                                        )}
                                                    >
                                                        <SelectValue placeholder="Choose relation" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="Father">Father</SelectItem>
                                                        <SelectItem value="Mother">Mother</SelectItem>
                                                        <SelectItem value="Uncle">Uncle</SelectItem>
                                                        <SelectItem value="Aunt">Aunt</SelectItem>
                                                        <SelectItem value="Sibling">Sibling</SelectItem>
                                                        <SelectItem value="Grandparent">Grandparent</SelectItem>
                                                        <SelectItem value="Guardian">Guardian</SelectItem>
                                                        <SelectItem value="Other">Other</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors.sponsorRelation && <p className="text-sm text-red-600">{errors.sponsorRelation}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="sponsorEmail" className="text-sm font-medium text-gray-700">
                                                    Email *
                                                </Label>
                                                <Input
                                                    id="sponsorEmail"
                                                    type="email"
                                                    value={formData.sponsorEmail}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, sponsorEmail: e.target.value }))}
                                                    className={cn(
                                                        "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.sponsorEmail && "border-red-500",
                                                    )}
                                                    placeholder="Enter sponsor's email"
                                                />
                                                {errors.sponsorEmail && <p className="text-sm text-red-600">{errors.sponsorEmail}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="sponsorPhone" className="text-sm font-medium text-gray-700">
                                                    Phone *
                                                </Label>
                                                <Input
                                                    id="sponsorPhone"
                                                    value={formData.sponsorPhone}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, sponsorPhone: e.target.value }))}
                                                    className={cn(
                                                        "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.sponsorPhone && "border-red-500",
                                                    )}
                                                    placeholder="Enter sponsor's phone number"
                                                />
                                                {errors.sponsorPhone && <p className="text-sm text-red-600">{errors.sponsorPhone}</p>}
                                            </div>

                                            <div className="space-y-2 md:col-span-2">
                                                <Label htmlFor="sponsorOccupation" className="text-sm font-medium text-gray-700">
                                                    Occupation *
                                                </Label>
                                                <Input
                                                    id="sponsorOccupation"
                                                    value={formData.sponsorOccupation}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, sponsorOccupation: e.target.value }))}
                                                    className={cn(
                                                        "h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.sponsorOccupation && "border-red-500",
                                                    )}
                                                    placeholder="Enter sponsor's occupation"
                                                />
                                                {errors.sponsorOccupation && <p className="text-sm text-red-600">{errors.sponsorOccupation}</p>}
                                            </div>
                                        </div>
                                    </motion.div>
                                )}

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.2 }}
                                    className="flex gap-4 pt-8"
                                >
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handlePrevious}
                                        className="flex-1 h-12 text-gray-600 border-gray-300 hover:bg-gray-50"
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                                    >
                                        {isLoading ? "Saving..." : "Save"}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </div>
    )
}
