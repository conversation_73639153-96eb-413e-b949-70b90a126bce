"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AuthModal } from "@/components/auth/auth-modal"
import {
    ArrowRight,
    CheckCircle,
    Linkedin,
    Instagram,
    Youtube,
    ChevronRight,
    Star,
    Users,
    GraduationCap,
    Globe,
    Award,
    ArrowUpRight,
    Menu,
    X,
    Facebook,
} from "lucide-react"

// Animation variants
const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
}

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
        },
    },
}

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
}

export default function Home() {
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
    const [scrolled, setScrolled] = useState(false)

    // Handle scroll effect for navbar
    useEffect(() => {
        const handleScroll = () => {
            setScrolled(window.scrollY > 20)
        }
        window.addEventListener("scroll", handleScroll)
        return () => window.removeEventListener("scroll", handleScroll)
    }, [])

    // Intersection observer hooks for animations
    const [heroRef, heroInView] = useInView({ triggerOnce: true, threshold: 0.1 })
    const [statsRef, statsInView] = useInView({ triggerOnce: true, threshold: 0.1 })
    const [featuresRef, featuresInView] = useInView({ triggerOnce: true, threshold: 0.1 })
    const [processRef, processInView] = useInView({ triggerOnce: true, threshold: 0.1 })
    const [testimonialsRef, testimonialsInView] = useInView({ triggerOnce: true, threshold: 0.1 })
    const [ctaRef, ctaInView] = useInView({ triggerOnce: true, threshold: 0.1 })

    // Testimonials data
    const testimonials = [
        {
            name: "Sarah Johnson",
            role: "Harvard University Student",
            content:
                "CLA 360 made my application process so much easier. I got accepted to my dream university with a scholarship!",
            avatar: "/placeholder.svg?height=60&width=60",
            rating: 5,
        },
        {
            name: "Michael Chen",
            role: "Stanford University Student",
            content:
                "The platform guided me through every step of the application process. Their visa assistance was particularly helpful.",
            avatar: "/placeholder.svg?height=60&width=60",
            rating: 5,
        },
        {
            name: "Emma Williams",
            role: "Oxford University Student",
            content:
                "Thanks to CLA 360, I was able to apply to multiple universities without the usual stress. Their support team was always available.",
            avatar: "/placeholder.svg?height=60&width=60",
            rating: 5,
        },
    ]

    // Stats data
    const stats = [
        { value: "10,000+", label: "Students Helped", icon: Users },
        { value: "500+", label: "Partner Universities", icon: GraduationCap },
        { value: "50+", label: "Countries", icon: Globe },
        { value: "95%", label: "Success Rate", icon: Award },
    ]

    // Features data
    const features = [
        {
            title: "Streamlined Applications",
            description: "Apply to multiple universities with a single application form, saving you time and effort.",
            icon: CheckCircle,
            color: "bg-blue-50 text-blue-600",
        },
        {
            title: "Scholarship Matching",
            description: "Get matched with scholarships that fit your profile and academic achievements.",
            icon: Award,
            color: "bg-purple-50 text-purple-600",
        },
        {
            title: "Visa Assistance",
            description: "Receive guidance on visa applications and requirements for studying abroad.",
            icon: Globe,
            color: "bg-emerald-50 text-emerald-600",
        },
        {
            title: "Accommodation Support",
            description: "Find and secure suitable accommodation near your chosen university.",
            icon: Users,
            color: "bg-amber-50 text-amber-600",
        },
        {
            title: "Career Guidance",
            description: "Access resources and advice to help plan your career path after graduation.",
            icon: GraduationCap,
            color: "bg-rose-50 text-rose-600",
        },
        {
            title: "24/7 Support",
            description: "Get help whenever you need it with our round-the-clock support team.",
            icon: CheckCircle,
            color: "bg-indigo-50 text-indigo-600",
        },
    ]

    // Process steps
    const processSteps = [
        {
            step: "01",
            title: "Create Your Profile",
            description:
                "Sign up and build your academic profile with your educational background, achievements, and preferences.",
        },
        {
            step: "02",
            title: "Explore Universities",
            description:
                "Browse through our extensive database of partner universities and find the perfect match for your academic goals.",
        },
        {
            step: "03",
            title: "Submit Applications",
            description: "Complete a single application form that can be sent to multiple universities of your choice.",
        },
        {
            step: "04",
            title: "Track Progress",
            description:
                "Monitor the status of your applications in real-time and receive notifications about important updates.",
        },
    ]

    return (
        <div className="flex min-h-screen flex-col">
            {/* Navbar */}
            <header
                className={`sticky top-0 z-50 w-full transition-all duration-300 ${scrolled
                    ? "border-b bg-white/90 backdrop-blur-md"
                    : "bg-gradient-to-r from-purple-50 to-blue-50 backdrop-blur-none"
                    }`}
            >
                <div className="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center gap-2">
                        <div className="relative h-10 w-10 overflow-hidden rounded-full bg-gradient-to-r from-blue-500 to-purple-600">
                            <Image
                                src="/placeholder.svg?height=40&width=40"
                                alt="CLA 360 Logo"
                                width={40}
                                height={40}
                                className="h-10 w-10 object-cover"
                            />
                        </div>
                        <span className="text-xl font-bold text-purple-800">CLA 360</span>
                    </div>

                    {/* Desktop Navigation */}
                    <nav className="hidden md:block">
                        <ul className="flex space-x-8">
                            <li>
                                <Link
                                    href="#"
                                    className="text-gray-700 transition-colors hover:text-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                                >
                                    Students
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="#"
                                    className="text-gray-700 transition-colors hover:text-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                                >
                                    Recruiters
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="#"
                                    className="text-gray-700 transition-colors hover:text-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                                >
                                    Institutions
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="#"
                                    className="text-gray-700 transition-colors hover:text-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                                >
                                    Blogs
                                </Link>
                            </li>
                            <li className="relative group">
                                <Link
                                    href="#"
                                    className="text-gray-700 transition-colors hover:text-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                                >
                                    More
                                </Link>
                            </li>
                        </ul>
                    </nav>

                    <div className="flex items-center gap-4">
                        <AuthModal
                            trigger={
                                <Button
                                    className="hidden bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-300 hover:from-blue-700 hover:to-purple-700 hover:shadow-md md:flex"
                                    size="sm"
                                >
                                    LOGIN
                                </Button>
                            }
                        />

                        {/* Mobile menu button */}
                        <Button
                            variant="ghost"
                            size="icon"
                            className="md:hidden"
                            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                        >
                            {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                        </Button>
                    </div>
                </div>

                {/* Mobile Navigation */}
                {mobileMenuOpen && (
                    <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        className="border-b bg-white md:hidden"
                    >
                        <nav className="container mx-auto px-4 py-4">
                            <ul className="space-y-4">
                                <li>
                                    <Link
                                        href="#"
                                        className="block text-gray-700 transition-colors hover:text-purple-700"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        Students
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="#"
                                        className="block text-gray-700 transition-colors hover:text-purple-700"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        Recruiters
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="#"
                                        className="block text-gray-700 transition-colors hover:text-purple-700"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        Institutions
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="#"
                                        className="block text-gray-700 transition-colors hover:text-purple-700"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        Blogs
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="#"
                                        className="block text-gray-700 transition-colors hover:text-purple-700"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        More
                                    </Link>
                                </li>
                                <li className="pt-2">
                                    <AuthModal
                                        trigger={
                                            <Button
                                                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                                                onClick={() => setMobileMenuOpen(false)}
                                            >
                                                LOGIN
                                            </Button>
                                        }
                                    />
                                </li>
                            </ul>
                        </nav>
                    </motion.div>
                )}
            </header>

            <main className="flex-1">
                {/* Hero Section */}
                <section
                    ref={heroRef}
                    className="relative overflow-hidden bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 py-16 sm:py-20 md:py-24 lg:py-32"
                >
                    {/* Background elements */}
                    <div className="absolute -top-24 -right-24 h-64 w-64 rounded-full bg-gradient-to-br from-purple-200/30 to-pink-200/30 blur-3xl"></div>
                    <div className="absolute bottom-0 left-0 h-64 w-64 rounded-full bg-gradient-to-tr from-blue-200/30 to-purple-200/30 blur-3xl"></div>
                    <div className="absolute top-1/2 left-1/4 h-32 w-32 -translate-x-1/2 -translate-y-1/2 rounded-full bg-yellow-200/20 blur-2xl"></div>

                    <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-8 lg:grid-cols-2 lg:gap-12">
                            <motion.div
                                initial="hidden"
                                animate={heroInView ? "visible" : "hidden"}
                                variants={fadeIn}
                                className="flex flex-col justify-center space-y-6 md:space-y-8"
                            >
                                <div>
                                    <Badge className="mb-4 bg-purple-100 px-4 py-1 text-purple-800 hover:bg-purple-200">
                                        Your Global Education Partner
                                    </Badge>
                                    <h1 className="mb-4 text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl md:mb-6 md:text-6xl">
                                        <span className="block">Apply Seamlessly to</span>
                                        <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                            Top Universities Abroad
                                        </span>
                                    </h1>
                                    <p className="text-lg text-gray-600 md:text-xl">
                                        Our innovative platform streamlines your university application process and connects you with top
                                        schools worldwide. Start your international education journey today!
                                    </p>
                                </div>

                                <div className="flex flex-col gap-4 sm:flex-row">
                                    <AuthModal
                                        defaultTab="signup"
                                        trigger={
                                            <Button
                                                size="lg"
                                                className="group relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-300 hover:from-blue-700 hover:to-purple-700 hover:shadow-lg"
                                            >
                                                <span className="relative z-10 flex items-center">
                                                    GET STARTED
                                                    <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                                                </span>
                                                <span className="absolute inset-0 -z-10 translate-y-full bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100"></span>
                                            </Button>
                                        }
                                    />
                                </div>

                                <div className="flex flex-wrap items-center gap-6">
                                    <div className="flex items-center gap-2">
                                        <span className="text-purple-700">Follow Us:</span>
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Link href="https://www.linkedin.com/company/college-league-app/">
                                            <Linkedin className="h-4 w-4 text-purple-700" />
                                        </Link>
                                        <Link href="https://www.instagram.com/collegeleagueapp/">
                                            <Instagram className="h-4 w-4 text-purple-700" />
                                        </Link>
                                        <Link href="https://www.youtube.com/@CollegeLeagueApp/videos">
                                            <Youtube className="h-4 w-4 text-purple-700" />
                                        </Link>
                                    </div>
                                </div>

                            </motion.div>

                            <motion.div
                                initial="hidden"
                                animate={heroInView ? "visible" : "hidden"}
                                variants={{
                                    hidden: { opacity: 0, scale: 0.95 },
                                    visible: { opacity: 1, scale: 1, transition: { duration: 0.6, delay: 0.2 } },
                                }}
                                className="relative mx-auto flex max-w-md items-center justify-center lg:max-w-none"
                            >
                                {/* Floating UI elements */}
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                                    transition={{ delay: 0.6, duration: 0.5 }}
                                    className="absolute -right-4 top-8 z-10 rounded-lg bg-white p-3 shadow-lg"
                                >
                                    <div className="flex items-center gap-2">
                                        <div className="h-3 w-3 rounded-full bg-green-400"></div>
                                        <span className="text-sm font-medium">Approved</span>
                                    </div>
                                    <div className="text-xs text-gray-500">Transcript</div>
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                                    transition={{ delay: 0.8, duration: 0.5 }}
                                    className="absolute -right-8 bottom-16 z-10 rounded-lg bg-white p-3 shadow-lg"
                                >
                                    <div className="flex items-center gap-2">
                                        <div className="h-6 w-6 rounded-full bg-blue-100 p-1">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="text-blue-600"
                                            >
                                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                                <circle cx="12" cy="7" r="4"></circle>
                                            </svg>
                                        </div>
                                        <div>
                                            <div className="text-sm font-medium">Student Id</div>
                                            <div className="text-xs text-gray-500">•••••••</div>
                                        </div>
                                    </div>
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                                    transition={{ delay: 1, duration: 0.5 }}
                                    className="absolute -left-4 bottom-8 z-10 rounded-lg bg-white p-3 shadow-lg"
                                >
                                    <div className="flex items-center gap-2">
                                        <div className="h-6 w-6 rounded-full bg-purple-100 p-1">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="text-purple-600"
                                            >
                                                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                                                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div className="text-sm font-medium">Notification</div>
                                            <div className="text-xs text-gray-500">Index - 10291</div>
                                        </div>
                                    </div>
                                </motion.div>

                                <motion.div
                                    animate={
                                        heroInView
                                            ? {
                                                y: [0, -10, 0],
                                                transition: {
                                                    y: {
                                                        repeat: Number.POSITIVE_INFINITY,
                                                        duration: 3,
                                                        ease: "easeInOut",
                                                    },
                                                },
                                            }
                                            : {}
                                    }
                                    className="relative h-[300px] w-[300px] overflow-hidden rounded-full bg-gradient-to-r from-blue-100 to-purple-100 p-2 sm:h-[350px] sm:w-[350px] md:h-[400px] md:w-[400px] lg:h-[450px] lg:w-[450px]"
                                >
                                    <div className="h-full w-full overflow-hidden rounded-full bg-white">
                                        <Image
                                            src="/placeholder.svg?height=500&width=500"
                                            alt="Student"
                                            width={500}
                                            height={500}
                                            className="h-full w-full object-cover"
                                        />
                                    </div>
                                </motion.div>
                            </motion.div>
                        </div>
                    </div>
                </section>

                {/* Trusted By Section */}
                <section className="border-y bg-white py-12">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                        <h2 className="mb-8 text-center text-lg font-medium text-gray-600">
                            Trusted by leading institutions worldwide
                        </h2>
                        <div className="relative overflow-hidden w-full mb-16">
                            <div className="flex animate-marquee whitespace-nowrap">
                                {[1, 2, 3, 4, 5, 6].map((i) => (
                                    <div key={i} className="mx-4 h-28">
                                        <Image
                                            src={`/${i}.jpeg`}
                                            alt={`Partner ${i}`}
                                            width={120}
                                            height={48}
                                            // className="h-12 object-contain"
                                        />
                                    </div>
                                ))}
                                {[1, 2, 3, 4, 5, 6].map((i) => (
                                    <div key={`dup-${i}`} className="mx-4 h-28">
                                        <Image
                                            src={`/${i}.jpeg`}
                                            alt={`Partner ${i}`}
                                            width={120}
                                            height={48}
                                            // className="h-12 object-contain"
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Stats Section - Simplified with clean design */}
                <section ref={statsRef} className="bg-white py-20">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                        <motion.h2
                            initial={{ opacity: 0, y: 20 }}
                            animate={statsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                            transition={{ duration: 0.6 }}
                            className="mb-12 text-center text-3xl font-bold text-gray-900 md:text-4xl"
                        >
                            Transforming Education Journeys
                        </motion.h2>
                        <motion.div
                            initial="hidden"
                            animate={statsInView ? "visible" : "hidden"}
                            variants={staggerContainer}
                            className="grid grid-cols-2 gap-8 md:grid-cols-4"
                        >
                            {stats.map((stat, index) => (
                                <motion.div
                                    key={index}
                                    variants={fadeInUp}
                                    className="flex flex-col items-center text-center"
                                    whileHover={{ scale: 1.05 }}
                                    transition={{ type: "spring", stiffness: 300 }}
                                >
                                    <div className="mb-4 rounded-full bg-purple-100 p-4 text-purple-600">
                                        <stat.icon className="h-8 w-8" />
                                    </div>
                                    <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                                    <div className="text-sm text-gray-600">{stat.label}</div>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>
                </section>

                {/* Features Section */}
                <section ref={featuresRef} className="bg-gray-50 py-20">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={featuresInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                            transition={{ duration: 0.6 }}
                            className="mx-auto mb-12 max-w-2xl text-center"
                        >
                            <Badge className="mb-4 bg-purple-100 px-4 py-1 text-purple-800 hover:bg-purple-200">Our Features</Badge>
                            <h2 className="text-3xl font-bold text-gray-900 md:text-4xl">
                                Everything You Need for Your Global Education
                            </h2>
                            <p className="mt-4 text-lg text-gray-600">
                                CLA 360 provides a comprehensive suite of tools to make your international education journey smooth and
                                successful.
                            </p>
                        </motion.div>

                        <motion.div
                            initial="hidden"
                            animate={featuresInView ? "visible" : "hidden"}
                            variants={staggerContainer}
                            className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
                        >
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    variants={fadeInUp}
                                    whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
                                    transition={{ type: "spring", stiffness: 300 }}
                                >
                                    <Card className="h-full overflow-hidden border-none shadow-md transition-all duration-300 hover:shadow-lg">
                                        <CardContent className="flex h-full flex-col p-6">
                                            <div className={`mb-4 inline-flex rounded-full p-3 ${feature.color}`}>
                                                <feature.icon className="h-6 w-6" />
                                            </div>
                                            <h3 className="mb-2 text-xl font-bold">{feature.title}</h3>
                                            <p className="flex-1 text-gray-600">{feature.description}</p>
                                            <div className="mt-4 flex items-center text-sm font-medium text-purple-600">
                                                <span>Learn more</span>
                                                <ArrowUpRight className="ml-1 h-4 w-4" />
                                            </div>
                                        </CardContent>
                                    </Card>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>
                </section>



                {/* How It Works Section */}
                <section ref={processRef} className="bg-gray-50 py-20">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={processInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                            transition={{ duration: 0.6 }}
                            className="mx-auto mb-12 max-w-2xl text-center"
                        >
                            <Badge className="mb-4 bg-purple-100 px-4 py-1 text-purple-800 hover:bg-purple-200">Simple Process</Badge>
                            <h2 className="text-3xl font-bold text-gray-900 md:text-4xl">How CLA 360 Works</h2>
                            <p className="mt-4 text-lg text-gray-600">
                                Our streamlined process makes applying to universities abroad simple and stress-free.
                            </p>
                        </motion.div>

                        <div className="relative">
                            <div className="absolute left-1/2 top-0 h-full w-1 -translate-x-1/2 bg-purple-100 md:block"></div>

                            <div className="space-y-12 md:space-y-24">
                                {processSteps.map((item, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={processInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                                        transition={{ duration: 0.6, delay: index * 0.2 }}
                                        className="relative"
                                    >
                                        <div className="absolute left-1/2 top-0 -mt-2 -translate-x-1/2 md:block">
                                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                                                {item.step}
                                            </div>
                                        </div>

                                        <div
                                            className={`ml-0 md:ml-${index % 2 === 0 ? "0" : "1/2"} w-full md:w-1/2 ${index % 2 === 0 ? "md:pr-12" : "md:pl-12"}`}
                                        >
                                            <Card className="overflow-hidden border-none shadow-md">
                                                <CardContent className="p-6">
                                                    <h3 className="mb-2 text-xl font-bold">{item.title}</h3>
                                                    <p className="text-gray-600">{item.description}</p>
                                                </CardContent>
                                            </Card>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Testimonials Section */}
                <section ref={testimonialsRef} className="bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 py-20">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={testimonialsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                            transition={{ duration: 0.6 }}
                            className="mx-auto mb-12 max-w-2xl text-center"
                        >
                            <Badge className="mb-4 bg-purple-100 px-4 py-1 text-purple-800 hover:bg-purple-200">
                                Success Stories
                            </Badge>
                            <h2 className="text-3xl font-bold text-gray-900 md:text-4xl">What Our Students Say</h2>
                            <p className="mt-4 text-lg text-gray-600">
                                Hear from students who have successfully used CLA 360 to achieve their international education goals.
                            </p>
                        </motion.div>

                        <motion.div
                            initial="hidden"
                            animate={testimonialsInView ? "visible" : "hidden"}
                            variants={staggerContainer}
                            className="grid gap-8 md:grid-cols-3"
                        >
                            {testimonials.map((testimonial, index) => (
                                <motion.div
                                    key={index}
                                    variants={fadeInUp}
                                    whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
                                >
                                    <Card className="h-full overflow-hidden border-none shadow-md transition-all duration-300 hover:shadow-lg">
                                        <CardContent className="flex h-full flex-col p-6">
                                            <div className="mb-4 flex items-center gap-4">
                                                <div className="h-12 w-12 overflow-hidden rounded-full">
                                                    <Image
                                                        src={testimonial.avatar || "/placeholder.svg"}
                                                        alt={testimonial.name}
                                                        width={48}
                                                        height={48}
                                                        className="h-full w-full object-cover"
                                                    />
                                                </div>
                                                <div>
                                                    <h3 className="font-bold">{testimonial.name}</h3>
                                                    <p className="text-sm text-gray-500">{testimonial.role}</p>
                                                </div>
                                            </div>

                                            <p className="mb-4 flex-1 text-gray-600">"{testimonial.content}"</p>

                                            <div className="flex text-yellow-400">
                                                {Array.from({ length: testimonial.rating }).map((_, i) => (
                                                    <Star key={i} className="h-5 w-5 fill-current" />
                                                ))}
                                            </div>
                                        </CardContent>
                                    </Card>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>
                </section>

                {/* CTA Section - Simplified with clean design */}
                <section ref={ctaRef} className="py-20">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={ctaInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                            transition={{ duration: 0.6 }}
                            className="overflow-hidden rounded-2xl bg-white shadow-xl"
                        >
                            <div className="grid md:grid-cols-2">
                                <div className="p-8 md:p-12">
                                    <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
                                        Ready to Start Your Global Education Journey?
                                    </h2>
                                    <p className="mb-8 text-lg text-gray-600">
                                        Join thousands of students who have successfully used CLA 360 to study at top universities
                                        worldwide.
                                    </p>
                                    <div className="flex flex-col gap-4 sm:flex-row">
                                        <AuthModal
                                            defaultTab="signup"
                                            trigger={
                                                <Button
                                                    size="lg"
                                                    className="group relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                                                >
                                                    <span className="relative z-10 flex items-center">
                                                        Get Started Now
                                                        <ChevronRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                                                    </span>
                                                </Button>
                                            }
                                        />
                                        <Button
                                            size="lg"
                                            variant="outline"
                                            className="border-purple-200 text-purple-700 hover:bg-purple-50 hover:text-purple-800"
                                        >
                                            Learn More
                                        </Button>
                                    </div>
                                </div>
                                <div className="hidden md:block">
                                    <div className="h-full w-full p-12">
                                        <div className="h-full w-full overflow-hidden rounded-lg bg-gray-100">
                                            <Image
                                                src="/placeholder.svg?height=300&width=300"
                                                alt="CTA Image"
                                                width={300}
                                                height={300}
                                                className="h-full w-full rounded-lg object-cover"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </section>
            </main>

            {/* Footer */}
            <footer className=" text-gray-900">
                <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                        <div>
                            <div className="mb-6 flex items-center gap-2">
                                <div className="relative h-10 w-10 overflow-hidden rounded-full bg-gradient-to-r from-blue-500 to-purple-600">
                                    <Image
                                        src="/placeholder.svg?height=40&width=40"
                                        alt="CLA 360 Logo"
                                        width={40}
                                        height={40}
                                        className="h-10 w-10 object-cover"
                                    />
                                </div>
                                <span className="text-xl font-bold">CLA 360</span>
                            </div>
                            <p className="mb-6 text-gray-400">
                                Your trusted partner for international education applications. We connect students with top universities
                                worldwide.
                            </p>
                            <div className="flex gap-4">
                                <Link
                                    href="#"
                                    className="rounded-full bg-gray-800 p-2 text-gray-400 transition-colors hover:bg-gray-700 hover:text-white"
                                >
                                    <Linkedin className="h-5 w-5" />
                                </Link>
                                <Link
                                    href="#"
                                    className="rounded-full bg-gray-800 p-2 text-gray-400 transition-colors hover:bg-gray-700 hover:text-white"
                                >
                                    <Instagram className="h-5 w-5" />
                                </Link>
                                <Link
                                    href="#"
                                    className="rounded-full bg-gray-800 p-2 text-gray-400 transition-colors hover:bg-gray-700 hover:text-white"
                                >
                                    <Youtube className="h-5 w-5" />
                                </Link>
                            </div>
                        </div>

                        <div>
                            <h3 className="mb-6 text-lg font-semibold">Company</h3>
                            <ul className="space-y-3">
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Home
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        About
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Events
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Contacts
                                    </Link>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 className="mb-6 text-lg font-semibold">Help Center</h3>
                            <ul className="space-y-3">
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Youtube
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Instagram
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Linkedin
                                    </Link>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 className="mb-6 text-lg font-semibold">Legal</h3>
                            <ul className="space-y-3">
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Privacy Policy
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Licensing
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="text-gray-400 transition-colors hover:text-white">
                                        Terms
                                    </Link>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div className="mt-12 border-t border-gray-800 pt-8 text-center">
                        <p className="text-sm text-gray-400">© 2025 CLA 360™. All Rights Reserved.</p>
                    </div>
                </div>
            </footer>
        </div>
    )
}
