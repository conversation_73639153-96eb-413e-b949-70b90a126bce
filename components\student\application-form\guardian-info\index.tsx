"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
    ArrowLeft,
    CheckCircle,
    Users,
    Phone,
    Mail,
    MapPin,
    AlertCircle,
    ChevronDown,
    Check,
    Loader2,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { countries } from "@/lib/countries"

// Animation variants
const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
}

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.1,
        },
    },
}

interface GuardianInfoForm {
    fullName: string
    relation: string
    email: string
    phone: string
    addressLine1: string
    addressLine2: string
    addressLine3: string
    country: string
    city: string
    zipCode: string
}

interface FormErrors {
    [key: string]: string
}

export default function GuardianInfoPage() {
    const router = useRouter()
    const [formData, setFormData] = useState<GuardianInfoForm>({
        fullName: "",
        relation: "",
        email: "",
        phone: "",
        addressLine1: "",
        addressLine2: "",
        addressLine3: "",
        country: "",
        city: "",
        zipCode: "",
    })

    const [errors, setErrors] = useState<FormErrors>({})
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [userAge, setUserAge] = useState<number | null>(null)
    const [openCountry, setOpenCountry] = useState(false)

    // Check if user should see this page
    useEffect(() => {
        const personalInfo = localStorage.getItem("personalInfo")
        if (personalInfo) {
            const parsed = JSON.parse(personalInfo)
            if (parsed.dateOfBirth) {
                const birthDate = new Date(parsed.dateOfBirth)
                const today = new Date()
                const age = today.getFullYear() - birthDate.getFullYear()
                const monthDiff = today.getMonth() - birthDate.getMonth()

                const calculatedAge =
                    monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate()) ? age - 1 : age

                setUserAge(calculatedAge)

                // If user is 18 or older, redirect to academic background
                if (calculatedAge > 17) {
                    router.push("/student/application-form/academic-background")
                    return
                }
            }
        } else {
            // If no personal info, redirect back to personal info
            router.push("/student/application-form/personal-info")
            return
        }

        // Load saved guardian data
        const savedData = localStorage.getItem("guardianInfo")
        if (savedData) {
            setFormData(JSON.parse(savedData))
        }
    }, [router])

    // Validation function
    const validateForm = (): boolean => {
        const newErrors: FormErrors = {}

        // Required fields validation
        if (!formData.fullName.trim()) newErrors.fullName = "Full name is required"
        if (!formData.relation) newErrors.relation = "Relation is required"
        if (!formData.email.trim()) newErrors.email = "Email is required"
        if (!formData.phone.trim()) newErrors.phone = "Phone is required"
        if (!formData.addressLine1.trim()) newErrors.addressLine1 = "Address line 1 is required"
        if (!formData.country) newErrors.country = "Country is required"
        if (!formData.city.trim()) newErrors.city = "City is required"
        if (!formData.zipCode.trim()) newErrors.zipCode = "Zip code is required"

        // Email validation
        if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = "Please enter a valid email address"
        }

        // Phone validation (basic)
        if (formData.phone && !/^\+?[\d\s\-$$$$]+$/.test(formData.phone)) {
            newErrors.phone = "Please enter a valid phone number"
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    // Handle input changes
    const handleInputChange = (field: keyof GuardianInfoForm, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }))
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: "" }))
        }
    }

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!validateForm()) {
            return
        }

        setIsSubmitting(true)

        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 2000))

            // Save form data
            localStorage.setItem("guardianInfo", JSON.stringify(formData))
            // Mark as completed
            localStorage.setItem("guardianInfo_completed", "true")

            // Trigger storage event for sidebar update
            window.dispatchEvent(new Event("storage"))

            // Navigate to next step
            router.push("/student/application-form/academic-background")
        } catch (error) {
            console.error("Error saving guardian info:", error)
        } finally {
            setIsSubmitting(false)
        }
    }

    // Handle previous button
    const handlePrevious = () => {
        router.push("/student/application-form/personal-info")
    }

    // Don't render if user age is not determined yet
    if (userAge === null) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
                <div className="flex items-center gap-3">
                    <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                    <p className="text-gray-600">Loading...</p>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
            <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
                {/* Modern Page Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="mb-8"
                >
                    <div className="text-center">
                        <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                            Guardian Information
                        </h1>
                        <p className="mt-2 text-lg text-gray-600 max-w-2xl mx-auto">
                            Please provide your guardian's contact and address information
                        </p>
                        <div className="mt-4 h-1 w-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mx-auto" />
                    </div>
                </motion.div>

                <motion.div initial="hidden" animate="visible" variants={staggerContainer} className="max-w-6xl mx-auto">
                    <Card className="border-none shadow-xl bg-white/80 backdrop-blur-sm">
                        <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
                            <CardTitle className="flex items-center gap-3 text-xl">
                                <div className="p-2 bg-white/20 rounded-lg">
                                    <Users className="h-6 w-6" />
                                </div>
                                Guardian Information Form
                            </CardTitle>
                            <CardDescription className="text-blue-100">
                                Required for students under 18. Fields marked with * are mandatory.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-6 sm:p-8">
                            <Alert className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                                <AlertCircle className="h-5 w-5 text-blue-600" />
                                <AlertDescription className="text-blue-800">
                                    <strong>Legal Requirement:</strong> This information is required for students under 18 years of age
                                    for legal guardianship verification and emergency contact purposes.
                                </AlertDescription>
                            </Alert>

                            <form onSubmit={handleSubmit} className="space-y-8">
                                {/* Guardian Details Section */}
                                <motion.div variants={fadeInUp} className="space-y-6">
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <Users className="h-5 w-5 text-blue-600" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900">Guardian Details</h3>
                                    </div>

                                    <div className="grid gap-6 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="fullName" className="text-sm font-medium text-gray-700">
                                                Full Name *
                                            </Label>
                                            <Input
                                                id="fullName"
                                                value={formData.fullName}
                                                onChange={(e) => handleInputChange("fullName", e.target.value)}
                                                className={cn(
                                                    "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.fullName && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                )}
                                                placeholder="Enter guardian's full name"
                                            />
                                            {errors.fullName && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.fullName}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium text-gray-700">Relation *</Label>
                                            <Select value={formData.relation} onValueChange={(value) => handleInputChange("relation", value)}>
                                                <SelectTrigger className={cn("h-11 border-gray-300", errors.relation && "border-red-500")}>
                                                    <SelectValue placeholder="Select relation" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="father">Father</SelectItem>
                                                    <SelectItem value="mother">Mother</SelectItem>
                                                    <SelectItem value="guardian">Legal Guardian</SelectItem>
                                                    <SelectItem value="grandfather">Grandfather</SelectItem>
                                                    <SelectItem value="grandmother">Grandmother</SelectItem>
                                                    <SelectItem value="uncle">Uncle</SelectItem>
                                                    <SelectItem value="aunt">Aunt</SelectItem>
                                                    <SelectItem value="stepfather">Stepfather</SelectItem>
                                                    <SelectItem value="stepmother">Stepmother</SelectItem>
                                                    <SelectItem value="other">Other Relative</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.relation && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.relation}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Contact Information Section */}
                                <motion.div variants={fadeInUp} className="space-y-6">
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <div className="p-2 bg-green-100 rounded-lg">
                                            <Phone className="h-5 w-5 text-green-600" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900">Contact Information</h3>
                                    </div>

                                    <div className="grid gap-6 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                                                Email Address *
                                            </Label>
                                            <div className="relative">
                                                <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    value={formData.email}
                                                    onChange={(e) => handleInputChange("email", e.target.value)}
                                                    className={cn(
                                                        "h-11 pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                        errors.email && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                    )}
                                                    placeholder="<EMAIL>"
                                                />
                                            </div>
                                            {errors.email && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.email}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                                                Phone Number *
                                            </Label>
                                            <div className="relative">
                                                <Phone className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                                <Input
                                                    id="phone"
                                                    value={formData.phone}
                                                    onChange={(e) => handleInputChange("phone", e.target.value)}
                                                    className={cn(
                                                        "h-11 pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                        errors.phone && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                    )}
                                                    placeholder="+****************"
                                                />
                                            </div>
                                            {errors.phone && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.phone}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Address Section */}
                                <motion.div variants={fadeInUp} className="space-y-6">
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <div className="p-2 bg-orange-100 rounded-lg">
                                            <MapPin className="h-5 w-5 text-orange-600" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900">Address Information</h3>
                                    </div>

                                    <div className="grid gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="addressLine1" className="text-sm font-medium text-gray-700">
                                                Address Line 1 *
                                            </Label>
                                            <Input
                                                id="addressLine1"
                                                value={formData.addressLine1}
                                                onChange={(e) => handleInputChange("addressLine1", e.target.value)}
                                                className={cn(
                                                    "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.addressLine1 && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                )}
                                                placeholder="Street address, P.O. box, company name, c/o"
                                            />
                                            {errors.addressLine1 && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.addressLine1}
                                                </p>
                                            )}
                                        </div>

                                        <div className="grid gap-4 sm:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="addressLine2" className="text-sm font-medium text-gray-700">
                                                    Address Line 2
                                                </Label>
                                                <Input
                                                    id="addressLine2"
                                                    value={formData.addressLine2}
                                                    onChange={(e) => handleInputChange("addressLine2", e.target.value)}
                                                    className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                                    placeholder="Apartment, suite, unit, building, floor, etc."
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="addressLine3" className="text-sm font-medium text-gray-700">
                                                    Address Line 3
                                                </Label>
                                                <Input
                                                    id="addressLine3"
                                                    value={formData.addressLine3}
                                                    onChange={(e) => handleInputChange("addressLine3", e.target.value)}
                                                    className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                                    placeholder="Additional address information"
                                                />
                                            </div>
                                        </div>

                                        <div className="grid gap-4 sm:grid-cols-3">
                                            <div className="space-y-2">
                                                <Label className="text-sm font-medium text-gray-700">Country *</Label>
                                                <Popover open={openCountry} onOpenChange={setOpenCountry}>
                                                    <PopoverTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            role="combobox"
                                                            aria-expanded={openCountry}
                                                            className={cn(
                                                                "h-11 w-full justify-between border-gray-300 hover:border-gray-400",
                                                                !formData.country && "text-muted-foreground",
                                                                errors.country && "border-red-500",
                                                            )}
                                                        >
                                                            {formData.country
                                                                ? countries.find((country) => country.code === formData.country)?.name
                                                                : "Select country"}
                                                            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                        </Button>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-full p-0" align="start">
                                                        <Command>
                                                            <CommandInput placeholder="Search countries..." />
                                                            <CommandList>
                                                                <CommandEmpty>No country found.</CommandEmpty>
                                                                <CommandGroup>
                                                                    {countries.map((country) => (
                                                                        <CommandItem
                                                                            key={country.code}
                                                                            value={country.name}
                                                                            onSelect={() => {
                                                                                handleInputChange("country", country.code)
                                                                                setOpenCountry(false)
                                                                            }}
                                                                        >
                                                                            <Check
                                                                                className={cn(
                                                                                    "mr-2 h-4 w-4",
                                                                                    formData.country === country.code ? "opacity-100" : "opacity-0",
                                                                                )}
                                                                            />
                                                                            {country.name}
                                                                        </CommandItem>
                                                                    ))}
                                                                </CommandGroup>
                                                            </CommandList>
                                                        </Command>
                                                    </PopoverContent>
                                                </Popover>
                                                {errors.country && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-4 w-4" />
                                                        {errors.country}
                                                    </p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="city" className="text-sm font-medium text-gray-700">
                                                    City *
                                                </Label>
                                                <Input
                                                    id="city"
                                                    value={formData.city}
                                                    onChange={(e) => handleInputChange("city", e.target.value)}
                                                    className={cn(
                                                        "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                        errors.city && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                    )}
                                                    placeholder="Enter city"
                                                />
                                                {errors.city && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-4 w-4" />
                                                        {errors.city}
                                                    </p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="zipCode" className="text-sm font-medium text-gray-700">
                                                    Zip/Postal Code *
                                                </Label>
                                                <Input
                                                    id="zipCode"
                                                    value={formData.zipCode}
                                                    onChange={(e) => handleInputChange("zipCode", e.target.value)}
                                                    className={cn(
                                                        "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                        errors.zipCode && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                    )}
                                                    placeholder="Enter zip/postal code"
                                                />
                                                {errors.zipCode && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-4 w-4" />
                                                        {errors.zipCode}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Navigation Buttons */}
                                <motion.div variants={fadeInUp} className="flex justify-between pt-6 border-t border-gray-200">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handlePrevious}
                                        className="min-w-[140px] h-12 border-gray-300 hover:border-gray-400"
                                    >
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Previous
                                    </Button>

                                    <Button
                                        type="submit"
                                        disabled={isSubmitting}
                                        className="min-w-[160px] h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                                    >
                                        {isSubmitting ? (
                                            <>
                                                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                                Saving...
                                            </>
                                        ) : (
                                            <>
                                                <CheckCircle className="mr-2 h-5 w-5" />
                                                Save & Continue
                                            </>
                                        )}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </div>
    )
}
