"use client"

import { SelectItem } from "@/components/ui/select"

import { SelectContent } from "@/components/ui/select"

import { SelectValue } from "@/components/ui/select"

import { SelectTrigger } from "@/components/ui/select"

import { Select } from "@/components/ui/select"

import { useState } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
    AlertCircle,
    ArrowRight,
    Bell,
    Calendar,
    CheckCircle,
    ChevronRight,
    Clock,
    Download,
    Eye,
    FileText,
    Filter,
    GraduationCap,
    HelpCircle,
    Info,
    Mail,
    MessageSquare,
    Search,
    X,
} from "lucide-react"

// Animation variants
const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
}

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
        },
    },
}

export default function TrackApplicationsPage() {
    const [ref, inView] = useInView({
        triggerOnce: true,
        threshold: 0.1,
    })

    const [activeTab, setActiveTab] = useState("all")

    // Mock data for applications
    const applications = [
        {
            id: "app-001",
            university: "Harvard University",
            program: "Computer Science, MS",
            status: "under_review",
            statusText: "Under Review",
            submittedDate: "May 5, 2025",
            lastUpdated: "May 15, 2025",
            deadline: "June 30, 2025",
            progress: 65,
            logo: "/placeholder.svg",
            steps: [
                { name: "Application Submitted", completed: true, date: "May 5, 2025" },
                { name: "Documents Verified", completed: true, date: "May 10, 2025" },
                { name: "Under Review", completed: true, date: "May 15, 2025" },
                { name: "Interview", completed: false },
                { name: "Decision", completed: false },
            ],
            notes: "Application is currently being reviewed by the admissions committee.",
        },
        {
            id: "app-002",
            university: "Stanford University",
            program: "Business Administration, MBA",
            status: "interview_scheduled",
            statusText: "Interview Scheduled",
            submittedDate: "April 20, 2025",
            lastUpdated: "May 18, 2025",
            deadline: "June 15, 2025",
            progress: 80,
            logo: "/placeholder.svg",
            steps: [
                { name: "Application Submitted", completed: true, date: "April 20, 2025" },
                { name: "Documents Verified", completed: true, date: "April 30, 2025" },
                { name: "Under Review", completed: true, date: "May 10, 2025" },
                { name: "Interview", completed: true, date: "May 25, 2025 (Scheduled)" },
                { name: "Decision", completed: false },
            ],
            notes: "Your interview has been scheduled for May 25, 2025 at 10:00 AM EST. Please check your email for details.",
        },
        {
            id: "app-003",
            university: "MIT",
            program: "Data Science, MS",
            status: "documents_required",
            statusText: "Documents Required",
            submittedDate: "May 8, 2025",
            lastUpdated: "May 12, 2025",
            deadline: "July 1, 2025",
            progress: 40,
            logo: "/placeholder.svg",
            steps: [
                { name: "Application Submitted", completed: true, date: "May 8, 2025" },
                { name: "Documents Verified", completed: false },
                { name: "Under Review", completed: false },
                { name: "Interview", completed: false },
                { name: "Decision", completed: false },
            ],
            notes:
                "Please submit your official transcript and recommendation letters. Your application cannot proceed without these documents.",
        },
    ]

    // Recent activity
    const recentActivity = [
        {
            id: "act-001",
            type: "status_change",
            university: "Harvard University",
            message: "Your application status has changed to 'Under Review'",
            date: "May 15, 2025",
        },
        {
            id: "act-002",
            type: "interview",
            university: "Stanford University",
            message: "Interview scheduled for May 25, 2025",
            date: "May 18, 2025",
        },
        {
            id: "act-003",
            type: "document",
            university: "MIT",
            message: "Additional documents requested",
            date: "May 12, 2025",
        },
        {
            id: "act-004",
            type: "deadline",
            university: "All Applications",
            message: "Stanford application deadline in 4 weeks",
            date: "May 18, 2025",
        },
    ]

    // Get status color based on status
    const getStatusColor = (status: string) => {
        switch (status) {
            case "accepted":
                return "bg-green-100 text-green-800"
            case "rejected":
                return "bg-red-100 text-red-800"
            case "under_review":
                return "bg-amber-100 text-amber-800"
            case "interview_scheduled":
                return "bg-purple-100 text-purple-800"
            case "documents_required":
                return "bg-blue-100 text-blue-800"
            case "waitlisted":
                return "bg-gray-100 text-gray-800"
            default:
                return "bg-gray-100 text-gray-800"
        }
    }

    // Get status icon based on status
    const getStatusIcon = (status: string) => {
        switch (status) {
            case "accepted":
                return <CheckCircle className="h-5 w-5 text-green-500" />
            case "rejected":
                return <X className="h-5 w-5 text-red-500" />
            case "under_review":
                return <Clock className="h-5 w-5 text-amber-500" />
            case "interview_scheduled":
                return <Calendar className="h-5 w-5 text-purple-500" />
            case "documents_required":
                return <FileText className="h-5 w-5 text-blue-500" />
            case "waitlisted":
                return <Clock className="h-5 w-5 text-gray-500" />
            default:
                return <HelpCircle className="h-5 w-5 text-gray-500" />
        }
    }

    // Get activity icon based on type
    const getActivityIcon = (type: string) => {
        switch (type) {
            case "status_change":
                return <Info className="h-5 w-5 text-blue-500" />
            case "interview":
                return <Calendar className="h-5 w-5 text-purple-500" />
            case "document":
                return <FileText className="h-5 w-5 text-amber-500" />
            case "deadline":
                return <Clock className="h-5 w-5 text-red-500" />
            case "message":
                return <MessageSquare className="h-5 w-5 text-green-500" />
            default:
                return <Bell className="h-5 w-5 text-gray-500" />
        }
    }

    return (
        <div className="container mx-auto p-6">
            {/* Page Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900">Track Applications</h1>
                <p className="text-gray-600">Monitor the status of your university applications in real-time</p>
            </div>

            {/* Main Content */}
            <div className="grid gap-6 md:grid-cols-3">
                {/* Left Column - Applications List */}
                <motion.div
                    ref={ref}
                    initial="hidden"
                    animate={inView ? "visible" : "hidden"}
                    variants={fadeInUp}
                    className="md:col-span-2"
                >
                    <Card className="border-none shadow-md">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle>Your Applications</CardTitle>
                                <div className="flex items-center gap-2">
                                    <Button variant="outline" size="sm" className="h-8 gap-1">
                                        <Filter className="h-3.5 w-3.5" />
                                        <span>Filter</span>
                                    </Button>
                                    <Button variant="outline" size="sm" className="h-8 gap-1">
                                        <Search className="h-3.5 w-3.5" />
                                        <span>Search</span>
                                    </Button>
                                </div>
                            </div>
                            <CardDescription>Track and manage all your university applications</CardDescription>
                        </CardHeader>

                        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
                            <div className="px-6">
                                <TabsList className="grid w-full grid-cols-4">
                                    <TabsTrigger
                                        value="all"
                                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                                    >
                                        All
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="in_progress"
                                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                                    >
                                        In Progress
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="action_required"
                                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                                    >
                                        Action Required
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="completed"
                                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                                    >
                                        Completed
                                    </TabsTrigger>
                                </TabsList>
                            </div>

                            <TabsContent value="all" className="px-0 pt-4">
                                <div className="space-y-4 px-6">
                                    {applications.map((app) => (
                                        <div key={app.id} className="rounded-lg border border-gray-100 p-4 transition-all hover:bg-gray-50">
                                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                                                <div className="flex items-center gap-3">
                                                    <div className="relative h-12 w-12 overflow-hidden rounded-lg">
                                                        <Image
                                                            src={app.logo || "/placeholder.svg"}
                                                            alt={app.university}
                                                            width={48}
                                                            height={48}
                                                            className="object-cover"
                                                        />
                                                    </div>
                                                    <div>
                                                        <h4 className="font-medium text-gray-900">{app.university}</h4>
                                                        <p className="text-xs text-gray-500">{app.program}</p>
                                                    </div>
                                                </div>
                                                <div className="ml-auto flex flex-col items-start gap-1 sm:items-end">
                                                    <Badge className={getStatusColor(app.status)}>
                                                        <span className="mr-1.5 flex items-center">{getStatusIcon(app.status)}</span>
                                                        {app.statusText}
                                                    </Badge>
                                                    <p className="text-xs text-gray-500">Last updated: {app.lastUpdated}</p>
                                                </div>
                                            </div>

                                            <div className="mt-4">
                                                <div className="mb-1 flex items-center justify-between">
                                                    <span className="text-xs font-medium">Application Progress</span>
                                                    <span className="text-xs text-gray-500">{app.progress}%</span>
                                                </div>
                                                <Progress value={app.progress} className="h-1.5" />
                                            </div>

                                            <div className="mt-4 flex flex-wrap items-center justify-between gap-2">
                                                <div className="flex flex-wrap gap-2">
                                                    <Button variant="outline" size="sm" className="h-8">
                                                        <Eye className="mr-1.5 h-3.5 w-3.5" />
                                                        View Details
                                                    </Button>
                                                    <Button variant="outline" size="sm" className="h-8">
                                                        <Download className="mr-1.5 h-3.5 w-3.5" />
                                                        Download
                                                    </Button>
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    Submitted: {app.submittedDate} | Deadline: {app.deadline}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </TabsContent>

                            {/* Other tab contents would be similar but filtered */}
                            <TabsContent value="in_progress" className="px-6 pt-4">
                                <div className="space-y-4">
                                    {applications
                                        .filter((app) => ["under_review", "interview_scheduled"].includes(app.status))
                                        .map((app) => (
                                            <div
                                                key={app.id}
                                                className="rounded-lg border border-gray-100 p-4 transition-all hover:bg-gray-50"
                                            >
                                                {/* Similar content as "all" tab */}
                                                <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                                                    <div className="flex items-center gap-3">
                                                        <div className="relative h-12 w-12 overflow-hidden rounded-lg">
                                                            <Image
                                                                src={app.logo || "/placeholder.svg"}
                                                                alt={app.university}
                                                                width={48}
                                                                height={48}
                                                                className="object-cover"
                                                            />
                                                        </div>
                                                        <div>
                                                            <h4 className="font-medium text-gray-900">{app.university}</h4>
                                                            <p className="text-xs text-gray-500">{app.program}</p>
                                                        </div>
                                                    </div>
                                                    <div className="ml-auto flex flex-col items-start gap-1 sm:items-end">
                                                        <Badge className={getStatusColor(app.status)}>
                                                            <span className="mr-1.5 flex items-center">{getStatusIcon(app.status)}</span>
                                                            {app.statusText}
                                                        </Badge>
                                                        <p className="text-xs text-gray-500">Last updated: {app.lastUpdated}</p>
                                                    </div>
                                                </div>

                                                <div className="mt-4">
                                                    <div className="mb-1 flex items-center justify-between">
                                                        <span className="text-xs font-medium">Application Progress</span>
                                                        <span className="text-xs text-gray-500">{app.progress}%</span>
                                                    </div>
                                                    <Progress value={app.progress} className="h-1.5" />
                                                </div>

                                                <div className="mt-4 flex flex-wrap items-center justify-between gap-2">
                                                    <div className="flex flex-wrap gap-2">
                                                        <Button variant="outline" size="sm" className="h-8">
                                                            <Eye className="mr-1.5 h-3.5 w-3.5" />
                                                            View Details
                                                        </Button>
                                                        <Button variant="outline" size="sm" className="h-8">
                                                            <Download className="mr-1.5 h-3.5 w-3.5" />
                                                            Download
                                                        </Button>
                                                    </div>
                                                    <div className="text-xs text-gray-500">
                                                        Submitted: {app.submittedDate} | Deadline: {app.deadline}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </TabsContent>

                            <TabsContent value="action_required" className="px-6 pt-4">
                                <div className="space-y-4">
                                    {applications
                                        .filter((app) => ["documents_required"].includes(app.status))
                                        .map((app) => (
                                            <div
                                                key={app.id}
                                                className="rounded-lg border border-gray-100 p-4 transition-all hover:bg-gray-50"
                                            >
                                                {/* Similar content as "all" tab */}
                                                <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                                                    <div className="flex items-center gap-3">
                                                        <div className="relative h-12 w-12 overflow-hidden rounded-lg">
                                                            <Image
                                                                src={app.logo || "/placeholder.svg"}
                                                                alt={app.university}
                                                                width={48}
                                                                height={48}
                                                                className="object-cover"
                                                            />
                                                        </div>
                                                        <div>
                                                            <h4 className="font-medium text-gray-900">{app.university}</h4>
                                                            <p className="text-xs text-gray-500">{app.program}</p>
                                                        </div>
                                                    </div>
                                                    <div className="ml-auto flex flex-col items-start gap-1 sm:items-end">
                                                        <Badge className={getStatusColor(app.status)}>
                                                            <span className="mr-1.5 flex items-center">{getStatusIcon(app.status)}</span>
                                                            {app.statusText}
                                                        </Badge>
                                                        <p className="text-xs text-gray-500">Last updated: {app.lastUpdated}</p>
                                                    </div>
                                                </div>

                                                <div className="mt-3 rounded-md bg-blue-50 p-3 text-sm text-blue-800">
                                                    <div className="flex items-start gap-2">
                                                        <AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0" />
                                                        <span>{app.notes}</span>
                                                    </div>
                                                </div>

                                                <div className="mt-4">
                                                    <div className="mb-1 flex items-center justify-between">
                                                        <span className="text-xs font-medium">Application Progress</span>
                                                        <span className="text-xs text-gray-500">{app.progress}%</span>
                                                    </div>
                                                    <Progress value={app.progress} className="h-1.5" />
                                                </div>

                                                <div className="mt-4 flex flex-wrap items-center justify-between gap-2">
                                                    <div className="flex flex-wrap gap-2">
                                                        <Button size="sm" className="h-8 bg-purple-600 hover:bg-purple-700">
                                                            <ArrowRight className="mr-1.5 h-3.5 w-3.5" />
                                                            Take Action
                                                        </Button>
                                                        <Button variant="outline" size="sm" className="h-8">
                                                            <Eye className="mr-1.5 h-3.5 w-3.5" />
                                                            View Details
                                                        </Button>
                                                    </div>
                                                    <div className="text-xs text-gray-500">
                                                        Submitted: {app.submittedDate} | Deadline: {app.deadline}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </TabsContent>

                            <TabsContent value="completed" className="px-6 pt-4">
                                <div className="space-y-4">
                                    {applications.filter((app) => ["accepted", "rejected", "waitlisted"].includes(app.status)).length ===
                                        0 ? (
                                        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-200 bg-gray-50 p-8 text-center">
                                            <div className="mb-3 rounded-full bg-gray-100 p-3">
                                                <GraduationCap className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <h3 className="mb-1 font-medium text-gray-900">No completed applications yet</h3>
                                            <p className="mb-4 text-sm text-gray-500">
                                                Your completed applications will appear here once decisions are made
                                            </p>
                                        </div>
                                    ) : (
                                        applications
                                            .filter((app) => ["accepted", "rejected", "waitlisted"].includes(app.status))
                                            .map((app) => (
                                                <div
                                                    key={app.id}
                                                    className="rounded-lg border border-gray-100 p-4 transition-all hover:bg-gray-50"
                                                >
                                                    {/* Similar content as "all" tab */}
                                                </div>
                                            ))
                                    )}
                                </div>
                            </TabsContent>
                        </Tabs>
                    </Card>
                </motion.div>

                {/* Right Column - Activity and Application Details */}
                <motion.div
                    initial="hidden"
                    animate={inView ? "visible" : "hidden"}
                    variants={fadeInUp}
                    className="md:col-span-1"
                >
                    {/* Recent Activity */}
                    <Card className="border-none shadow-md">
                        <CardHeader className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                            <CardTitle className="text-lg">Recent Activity</CardTitle>
                            <CardDescription className="text-blue-100">Latest updates on your applications</CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="divide-y divide-gray-100">
                                {recentActivity.map((activity) => (
                                    <div key={activity.id} className="p-4 hover:bg-gray-50">
                                        <div className="flex items-start gap-3">
                                            <div className="rounded-full bg-gray-100 p-2 text-gray-600">{getActivityIcon(activity.type)}</div>
                                            <div className="flex-1">
                                                <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                                                <div className="mt-1 flex items-center justify-between">
                                                    <span className="text-xs text-gray-500">{activity.university}</span>
                                                    <span className="text-xs text-gray-500">{activity.date}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                        <CardFooter className="border-t border-gray-100 p-4">
                            <Button variant="ghost" className="w-full justify-between text-purple-600 hover:text-purple-700">
                                <span>View All Activity</span>
                                <ChevronRight className="h-4 w-4" />
                            </Button>
                        </CardFooter>
                    </Card>

                    {/* Application Timeline */}
                    <motion.div initial="hidden" animate={inView ? "visible" : "hidden"} variants={fadeInUp} className="mt-6">
                        <Card className="border-none shadow-md">
                            <CardHeader className="pb-2">
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-lg">Application Timeline</CardTitle>
                                    <Select defaultValue={applications[0].id} className="hidden">
                                        <SelectTrigger className="h-8 w-[180px]">
                                            <SelectValue placeholder="Select University" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {applications.map((app) => (
                                                <SelectItem key={app.id} value={app.id}>
                                                    {app.university}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <CardDescription>Harvard University - Computer Science, MS</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="relative mt-4 pb-4">
                                    {/* Timeline line */}
                                    <div className="absolute left-6 top-0 h-full w-0.5 -translate-x-1/2 bg-gray-100"></div>

                                    <div className="space-y-8">
                                        {applications[0].steps.map((step, index) => (
                                            <div key={index} className="relative">
                                                {/* Step indicator */}
                                                <div
                                                    className={`absolute left-6 top-0 flex h-12 w-12 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full border-4 border-white ${step.completed ? "bg-green-500 text-white" : "bg-white text-gray-400"
                                                        }`}
                                                >
                                                    {step.completed ? <CheckCircle className="h-5 w-5" /> : <Clock className="h-5 w-5" />}
                                                </div>

                                                <div className="ml-12 pt-1">
                                                    <h3 className={`text-lg font-bold ${step.completed ? "text-gray-900" : "text-gray-600"}`}>
                                                        {step.name}
                                                    </h3>
                                                    {step.date && <p className="mt-1 text-sm text-gray-500">{step.date}</p>}

                                                    {step.completed ? (
                                                        <Badge className="mt-2 bg-green-100 text-green-800">Completed</Badge>
                                                    ) : index === applications[0].steps.findIndex((s) => !s.completed) ? (
                                                        <Badge className="mt-2 bg-amber-100 text-amber-800">Current Stage</Badge>
                                                    ) : (
                                                        <Badge className="mt-2 bg-gray-100 text-gray-800">Pending</Badge>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>

                    {/* Help Section */}
                    <motion.div initial="hidden" animate={inView ? "visible" : "hidden"} variants={fadeInUp} className="mt-6">
                        <Card className="border-none shadow-md">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-lg">Need Help?</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <Button variant="outline" className="w-full justify-start">
                                        <Mail className="mr-2 h-4 w-4" />
                                        Contact Admissions Support
                                    </Button>
                                    <Button variant="outline" className="w-full justify-start">
                                        <HelpCircle className="mr-2 h-4 w-4" />
                                        Application FAQs
                                    </Button>
                                    <Button variant="outline" className="w-full justify-start">
                                        <MessageSquare className="mr-2 h-4 w-4" />
                                        Chat with an Advisor
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>
                </motion.div>
            </div>
        </div>
    )
}
