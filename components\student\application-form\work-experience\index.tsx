"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Briefcase, X } from "lucide-react"
import { format } from "date-fns"

interface WorkExperience {
    id: string
    company: string
    position: string
    responsibilities: string
    startDate: Date | undefined
    endDate: Date | undefined
}

interface FormData {
    workExperiences: WorkExperience[]
}

export default function WorkExperiencePage() {
    const router = useRouter()
    const [formData, setFormData] = useState<FormData>({
        workExperiences: [],
    })
    const [newExperience, setNewExperience] = useState<WorkExperience>({
        id: "",
        company: "",
        position: "",
        responsibilities: "",
        startDate: undefined,
        endDate: undefined,
    })
    const [isLoading, setIsLoading] = useState(false)

    useEffect(() => {
        const saved = localStorage.getItem("workExperience_data")
        if (saved) {
            const parsedData = JSON.parse(saved)
            setFormData({
                workExperiences: parsedData.workExperiences.map((exp: any) => ({
                    ...exp,
                    startDate: exp.startDate ? new Date(exp.startDate) : undefined,
                    endDate: exp.endDate ? new Date(exp.endDate) : undefined,
                })),
            })
        }
    }, [])

    const addExperience = () => {
        if (newExperience.company.trim() && newExperience.position.trim()) {
            setFormData((prev) => ({
                ...prev,
                workExperiences: [...prev.workExperiences, { ...newExperience, id: Date.now().toString() }],
            }))
            setNewExperience({
                id: "",
                company: "",
                position: "",
                responsibilities: "",
                startDate: undefined,
                endDate: undefined,
            })
        }
    }

    const removeExperience = (id: string) => {
        setFormData((prev) => ({
            ...prev,
            workExperiences: prev.workExperiences.filter((exp) => exp.id !== id),
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)

        localStorage.setItem("workExperience_data", JSON.stringify(formData))
        localStorage.setItem("workExperience_completed", "true")

        window.dispatchEvent(new Event("formStatusUpdate"))

        await new Promise((resolve) => setTimeout(resolve, 1000))
        setIsLoading(false)

        router.push("/student/application-form/health-info")
    }

    const handlePrevious = () => {
        localStorage.setItem("workExperience_data", JSON.stringify(formData))
        router.push("/student/application-form/extracurricular-activities")
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            <div className="container mx-auto px-4 py-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <Card className="max-w-6xl mx-auto border-none shadow-xl bg-white/80 backdrop-blur-sm">
                        <CardHeader className="text-center pb-8">
                            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
                                <Briefcase className="w-8 h-8 text-white" />
                            </div>
                            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                Work Experience
                            </CardTitle>
                        </CardHeader>
                        <CardHeader className="text-center pb-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm"></div>
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{
                                    delay: 0.2,
                                    type: "spring",
                                    stiffness: 200,
                                }}
                                className="relative mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-6 backdrop-blur-sm"
                            >
                                <Briefcase className="w-8 h-8 text-white" />
                            </motion.div>
                            <CardTitle className="text-4xl font-bold relative">
                                {" "}
                                Work Experience
                            </CardTitle>
                            <p className="text-purple-100 mt-2 relative">
                                {" "}
                                Add previous work experience
                            </p>
                        </CardHeader>

                        <CardContent className="space-y-8 pt-2">
                            <form onSubmit={handleSubmit} className="space-y-8">
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                    className="space-y-6"
                                >
                                    <div className="text-center">
                                        <h3 className="text-lg font-semibold text-purple-600 mb-1">
                                            Previous work experience
                                        </h3>
                                    </div>

                                    {/* Add New Experience Form */}
                                    <div className="bg-gray-50 p-6 rounded-lg space-y-4">
                                        <div className="grid md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label
                                                    htmlFor="company"
                                                    className="text-sm font-medium text-gray-700"
                                                >
                                                    Company
                                                </Label>
                                                <Input
                                                    id="company"
                                                    value={
                                                        newExperience.company
                                                    }
                                                    onChange={(e) =>
                                                        setNewExperience(
                                                            (prev) => ({
                                                                ...prev,
                                                                company:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        )
                                                    }
                                                    placeholder="HealthCare Plus"
                                                    className="h-10"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label
                                                    htmlFor="position"
                                                    className="text-sm font-medium text-gray-700"
                                                >
                                                    Position
                                                </Label>
                                                <Input
                                                    id="position"
                                                    value={
                                                        newExperience.position
                                                    }
                                                    onChange={(e) =>
                                                        setNewExperience(
                                                            (prev) => ({
                                                                ...prev,
                                                                position:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        )
                                                    }
                                                    placeholder="Data Analyst"
                                                    className="h-10"
                                                />
                                            </div>

                                            <div className="space-y-2 md:col-span-2">
                                                <Label
                                                    htmlFor="responsibilities"
                                                    className="text-sm font-medium text-gray-700"
                                                >
                                                    Summary of responsibilities
                                                </Label>
                                                <Textarea
                                                    id="responsibilities"
                                                    value={
                                                        newExperience.responsibilities
                                                    }
                                                    onChange={(e) =>
                                                        setNewExperience(
                                                            (prev) => ({
                                                                ...prev,
                                                                responsibilities:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        )
                                                    }
                                                    placeholder="eg. Analyzed patient data to identify trends and support healthcare initiatives."
                                                    className="min-h-[80px]"
                                                />
                                            </div>

                                            <div className="space-y-2 md:col-span-2">
                                                <Label className="text-sm font-medium text-gray-700">
                                                    Work period
                                                </Label>
                                                <div className="flex items-center gap-2">
                                                    <Popover>
                                                        <PopoverTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                className="flex-1 justify-start text-left font-normal h-10"
                                                            >
                                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                                {newExperience.startDate
                                                                    ? format(
                                                                        newExperience.startDate,
                                                                        "PPP"
                                                                    )
                                                                    : "start"}
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent
                                                            className="w-auto p-0"
                                                            align="start"
                                                        >
                                                            <Calendar
                                                                mode="single"
                                                                selected={
                                                                    newExperience.startDate
                                                                }
                                                                onSelect={(
                                                                    date
                                                                ) =>
                                                                    setNewExperience(
                                                                        (
                                                                            prev
                                                                        ) => ({
                                                                            ...prev,
                                                                            startDate:
                                                                                date,
                                                                        })
                                                                    )
                                                                }
                                                                initialFocus
                                                            />
                                                        </PopoverContent>
                                                    </Popover>
                                                    <span className="text-sm text-gray-500">
                                                        to
                                                    </span>
                                                    <Popover>
                                                        <PopoverTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                className="flex-1 justify-start text-left font-normal h-10"
                                                            >
                                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                                {newExperience.endDate
                                                                    ? format(
                                                                        newExperience.endDate,
                                                                        "PPP"
                                                                    )
                                                                    : "end"}
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent
                                                            className="w-auto p-0"
                                                            align="start"
                                                        >
                                                            <Calendar
                                                                mode="single"
                                                                selected={
                                                                    newExperience.endDate
                                                                }
                                                                onSelect={(
                                                                    date
                                                                ) =>
                                                                    setNewExperience(
                                                                        (
                                                                            prev
                                                                        ) => ({
                                                                            ...prev,
                                                                            endDate:
                                                                                date,
                                                                        })
                                                                    )
                                                                }
                                                                initialFocus
                                                            />
                                                        </PopoverContent>
                                                    </Popover>
                                                </div>
                                            </div>
                                        </div>

                                        <Button
                                            type="button"
                                            onClick={addExperience}
                                            size="sm"
                                            className="w-full"
                                        >
                                            Add
                                        </Button>
                                    </div>

                                    {/* Display Added Experiences */}
                                    {formData.workExperiences.map(
                                        (experience) => (
                                            <div
                                                key={experience.id}
                                                className="bg-white border border-gray-200 p-6 rounded-lg space-y-3"
                                            >
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <h4 className="font-semibold text-gray-800">
                                                            {experience.company}
                                                        </h4>
                                                        <p className="text-sm text-gray-600 font-medium">
                                                            {
                                                                experience.position
                                                            }
                                                        </p>
                                                        <p className="text-sm text-gray-600 mt-2">
                                                            {
                                                                experience.responsibilities
                                                            }
                                                        </p>
                                                        <p className="text-xs text-gray-500 mt-2">
                                                            {experience.startDate
                                                                ? format(
                                                                    experience.startDate,
                                                                    "MMM yyyy"
                                                                )
                                                                : "Start"}{" "}
                                                            -{" "}
                                                            {experience.endDate
                                                                ? format(
                                                                    experience.endDate,
                                                                    "MMM yyyy"
                                                                )
                                                                : "End"}
                                                        </p>
                                                    </div>
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() =>
                                                            removeExperience(
                                                                experience.id
                                                            )
                                                        }
                                                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        )
                                    )}
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.2 }}
                                    className="flex gap-4 pt-8"
                                >
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handlePrevious}
                                        className="flex-1 h-12 text-gray-600 border-gray-300 hover:bg-gray-50"
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                                    >
                                        {isLoading ? "Saving..." : "Save"}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </div>
    );
}
