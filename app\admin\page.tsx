import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Users, School, FileText, DollarSign } from "lucide-react"

export default function AdminDashboard() {
  // Dummy data for the dashboard
  const stats = [
    {
      title: "Total Students",
      value: "1,248",
      icon: Users,
      change: "+12% from last month",
      positive: true,
    },
    {
      title: "Partner Universities",
      value: "64",
      icon: School,
      change: "+3 new this month",
      positive: true,
    },
    {
      title: "Applications",
      value: "3,157",
      icon: FileText,
      change: "+24% from last month",
      positive: true,
    },
    {
      title: "Revenue",
      value: "$42,580",
      icon: DollarSign,
      change: "+18% from last month",
      positive: true,
    },
  ]

  // Dummy data for recent applications
  const recentApplications = [
    {
      id: "APP-7829",
      student: "<PERSON>",
      university: "Harvard University",
      program: "Computer Science",
      status: "Pending",
      date: "2025-05-18",
    },
    {
      id: "APP-7830",
      student: "<PERSON>",
      university: "Stanford University",
      program: "Business Administration",
      status: "Approved",
      date: "2025-05-17",
    },
    {
      id: "APP-7831",
      student: "<PERSON>",
      university: "MIT",
      program: "Electrical Engineering",
      status: "Pending",
      date: "2025-05-16",
    },
    {
      id: "APP-7832",
      student: "Emily Williams",
      university: "Oxford University",
      program: "International Relations",
      status: "Rejected",
      date: "2025-05-15",
    },
    {
      id: "APP-7833",
      student: "David Brown",
      university: "Cambridge University",
      program: "Physics",
      status: "Pending",
      date: "2025-05-14",
    },
  ]

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Admin Dashboard</h1>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                  <h3 className="text-2xl font-bold">{stat.value}</h3>
                  <p className={`text-xs ${stat.positive ? "text-green-600" : "text-red-600"}`}>{stat.change}</p>
                </div>
                <div className="rounded-full bg-purple-100 p-3 text-purple-600">
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b text-left">
                  <th className="pb-3 pr-4 font-medium">ID</th>
                  <th className="pb-3 pr-4 font-medium">Student</th>
                  <th className="pb-3 pr-4 font-medium">University</th>
                  <th className="pb-3 pr-4 font-medium">Program</th>
                  <th className="pb-3 pr-4 font-medium">Status</th>
                  <th className="pb-3 pr-4 font-medium">Date</th>
                </tr>
              </thead>
              <tbody>
                {recentApplications.map((app) => (
                  <tr key={app.id} className="border-b">
                    <td className="py-3 pr-4">{app.id}</td>
                    <td className="py-3 pr-4">{app.student}</td>
                    <td className="py-3 pr-4">{app.university}</td>
                    <td className="py-3 pr-4">{app.program}</td>
                    <td className="py-3 pr-4">
                      <span
                        className={`inline-block rounded-full px-2 py-1 text-xs font-medium ${
                          app.status === "Approved"
                            ? "bg-green-100 text-green-800"
                            : app.status === "Rejected"
                              ? "bg-red-100 text-red-800"
                              : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {app.status}
                      </span>
                    </td>
                    <td className="py-3 pr-4">{app.date}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
