import type React from "react"
import Link from "next/link"
import Image from "next/image"
import { AdminSidebar } from "@/components/admin/sidebar"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="border-b bg-white">
        <div className="container mx-auto flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <Image
              src="/placeholder.svg?height=40&width=40"
              alt="CLA 360 Logo"
              width={40}
              height={40}
              className="h-10 w-10"
            />
            <Link href="/admin" className="text-2xl font-bold text-purple-800">
              CLA 360 Admin
            </Link>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm text-gray-500">Admin Panel</div>
            </div>
            <div className="h-10 w-10 overflow-hidden rounded-full bg-gray-200">
              <Image src="/placeholder.svg?height=40&width=40" alt="Admin" width={40} height={40} />
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-1">
        <AdminSidebar />
        <main className="flex-1 p-6">{children}</main>
      </div>

      <footer className="border-t bg-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center justify-center gap-2">
            <div className="flex items-center gap-2">
              <Image
                src="/placeholder.svg?height=30&width=30"
                alt="CLA 360 Logo"
                width={30}
                height={30}
                className="h-8 w-8"
              />
              <span className="text-lg font-bold text-purple-800">CLA 360 - College League App</span>
            </div>
            <p className="text-xs text-gray-500">© 2025 CLA 360™. All Rights Reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
