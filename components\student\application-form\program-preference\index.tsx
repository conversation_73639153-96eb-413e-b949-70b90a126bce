"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BookOpen } from "lucide-react"
import { cn } from "@/lib/utils"
import { countries } from "@/lib/countries"

interface FormData {
    intendedFieldOfStudy: string
    secondaryFieldOfStudy: string
    preferredStartTerm: string
    preferredStartYear: string
    preferredCountry: string
}

export default function ProgramPreferencePage() {
    const router = useRouter()
    const [formData, setFormData] = useState<FormData>({
        intendedFieldOfStudy: "",
        secondaryFieldOfStudy: "",
        preferredStartTerm: "",
        preferredStartYear: "",
        preferredCountry: "",
    })
    const [errors, setErrors] = useState<Record<string, string>>({})
    const [isLoading, setIsLoading] = useState(false)

    useEffect(() => {
        const saved = localStorage.getItem("programPreference_data")
        if (saved) {
            setFormData(JSON.parse(saved))
        }
    }, [])

    const validateForm = () => {
        const newErrors: Record<string, string> = {}

        if (!formData.intendedFieldOfStudy.trim()) {
            newErrors.intendedFieldOfStudy = "Intended field of study is required"
        }
        if (!formData.preferredStartTerm) {
            newErrors.preferredStartTerm = "Preferred start term is required"
        }
        if (!formData.preferredStartYear) {
            newErrors.preferredStartYear = "Preferred start year is required"
        }
        if (!formData.preferredCountry) {
            newErrors.preferredCountry = "Preferred country is required"
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!validateForm()) return

        setIsLoading(true)

        localStorage.setItem("programPreference_data", JSON.stringify(formData))
        localStorage.setItem("programPreference_completed", "true")

        window.dispatchEvent(new Event("formStatusUpdate"))

        await new Promise((resolve) => setTimeout(resolve, 1000))
        setIsLoading(false)

        router.push("/student/application-form/financial-info")
    }

    const handlePrevious = () => {
        localStorage.setItem("programPreference_data", JSON.stringify(formData))
        router.push("/student/application-form/academic-background")
    }

    const currentYear = new Date().getFullYear()
    const years = Array.from({ length: 5 }, (_, i) => currentYear + i)

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            <div className="container mx-auto px-4 py-8">
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
                    <Card className="max-w-6xl mx-auto border-none shadow-xl bg-white/80 backdrop-blur-sm">

                        <CardHeader className="text-center pb-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm"></div>
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                                className="relative mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-6 backdrop-blur-sm"
                            >
                                <BookOpen className="w-8 h-8 text-white" />
                            </motion.div>
                            <CardTitle className="text-4xl font-bold relative">Program Preference</CardTitle>
                            <p className="text-purple-100 mt-2 relative"> Please provide your program preference details.</p>
                        </CardHeader>

                        <CardContent className="space-y-8 pt-2">
                            <form onSubmit={handleSubmit} className="space-y-8">
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                    className="grid md:grid-cols-2 gap-6"
                                >
                                    <div className="space-y-2">
                                        <Label htmlFor="intendedFieldOfStudy" className="text-sm font-medium text-gray-700">
                                            Intended field of study *
                                        </Label>
                                        <Input
                                            id="intendedFieldOfStudy"
                                            value={formData.intendedFieldOfStudy}
                                            onChange={(e) => setFormData((prev) => ({ ...prev, intendedFieldOfStudy: e.target.value }))}
                                            className={cn(
                                                "h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                errors.intendedFieldOfStudy && "border-red-500",
                                            )}
                                            placeholder="e.g., Computer Science, Business Administration"
                                        />
                                        {errors.intendedFieldOfStudy && (
                                            <p className="text-sm text-red-600">{errors.intendedFieldOfStudy}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="secondaryFieldOfStudy" className="text-sm font-medium text-gray-700">
                                            Secondary field of study
                                        </Label>
                                        <Input
                                            id="secondaryFieldOfStudy"
                                            value={formData.secondaryFieldOfStudy}
                                            onChange={(e) => setFormData((prev) => ({ ...prev, secondaryFieldOfStudy: e.target.value }))}
                                            className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                            placeholder="e.g., Mathematics, Psychology (optional)"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label className="text-sm font-medium text-gray-700">Preferred start term *</Label>
                                        <Select
                                            value={formData.preferredStartTerm}
                                            onValueChange={(value) => setFormData((prev) => ({ ...prev, preferredStartTerm: value }))}
                                        >
                                            <SelectTrigger
                                                className={cn(
                                                    "h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.preferredStartTerm && "border-red-500",
                                                )}
                                            >
                                                <SelectValue placeholder="Choose term" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="fall">Fall</SelectItem>
                                                <SelectItem value="spring">Spring</SelectItem>
                                                <SelectItem value="summer">Summer</SelectItem>
                                                <SelectItem value="winter">Winter</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.preferredStartTerm && <p className="text-sm text-red-600">{errors.preferredStartTerm}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label className="text-sm font-medium text-gray-700">Preferred start year *</Label>
                                        <Select
                                            value={formData.preferredStartYear}
                                            onValueChange={(value) => setFormData((prev) => ({ ...prev, preferredStartYear: value }))}
                                        >
                                            <SelectTrigger
                                                className={cn(
                                                    "h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.preferredStartYear && "border-red-500",
                                                )}
                                            >
                                                <SelectValue placeholder="Choose year" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {years.map((year) => (
                                                    <SelectItem key={year} value={year.toString()}>
                                                        {year}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.preferredStartYear && <p className="text-sm text-red-600">{errors.preferredStartYear}</p>}
                                    </div>

                                    <div className="space-y-2 md:col-span-2">
                                        <Label className="text-sm font-medium text-gray-700">Preferred country *</Label>
                                        <Select
                                            value={formData.preferredCountry}
                                            onValueChange={(value) => setFormData((prev) => ({ ...prev, preferredCountry: value }))}
                                        >
                                            <SelectTrigger
                                                className={cn(
                                                    "h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.preferredCountry && "border-red-500",
                                                )}
                                            >
                                                <SelectValue placeholder="Choose country" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {countries.map((country) => (
                                                    <SelectItem key={country.code} value={country.name}>
                                                        {country.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.preferredCountry && <p className="text-sm text-red-600">{errors.preferredCountry}</p>}
                                    </div>
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.2 }}
                                    className="flex gap-4 pt-8"
                                >
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handlePrevious}
                                        className="flex-1 h-12 text-gray-600 border-gray-300 hover:bg-gray-50"
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                                    >
                                        {isLoading ? "Saving..." : "Save"}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </div>
    )
}
