"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Upload, Plus, X, FileText, BookOpen, GraduationCap, CheckCircle, Loader2, DollarSign } from "lucide-react"

interface ResearchTopic {
    id: string
    topic: string
}

interface Publication {
    id: string
    publication: string
}

export default function FundingHubPage() {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)

    // Form state
    const [cvFile, setCvFile] = useState<File | null>(null)
    const [researchTopics, setResearchTopics] = useState<ResearchTopic[]>([])
    const [newResearchTopic, setNewResearchTopic] = useState("")
    const [publications, setPublications] = useState<Publication[]>([])
    const [newPublication, setNewPublication] = useState("")
    const [optForAssistantship, setOptForAssistantship] = useState(false)

    // Load saved data on component mount
    useEffect(() => {
        const savedData = localStorage.getItem("fundingHub_data")
        if (savedData) {
            const data = JSON.parse(savedData)
            setResearchTopics(data.researchTopics || [])
            setPublications(data.publications || [])
            setOptForAssistantship(data.optForAssistantship || false)
        }
    }, [])

    // Save data to localStorage
    const saveData = () => {
        const data = {
            researchTopics,
            publications,
            optForAssistantship,
            cvUploaded: !!cvFile,
        }
        localStorage.setItem("fundingHub_data", JSON.stringify(data))
    }

    // Handle CV file upload
    const handleCvUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file) {
            setCvFile(file)
        }
    }

    // Add research topic
    const addResearchTopic = () => {
        if (newResearchTopic.trim()) {
            const newTopic: ResearchTopic = {
                id: Date.now().toString(),
                topic: newResearchTopic.trim(),
            }
            setResearchTopics([...researchTopics, newTopic])
            setNewResearchTopic("")
        }
    }

    // Remove research topic
    const removeResearchTopic = (id: string) => {
        setResearchTopics(researchTopics.filter((topic) => topic.id !== id))
    }

    // Add publication
    const addPublication = () => {
        if (newPublication.trim()) {
            const publication: Publication = {
                id: Date.now().toString(),
                publication: newPublication.trim(),
            }
            setPublications([...publications, publication])
            setNewPublication("")
        }
    }

    // Remove publication
    const removePublication = (id: string) => {
        setPublications(publications.filter((pub) => pub.id !== id))
    }

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)

        // Save data
        saveData()

        // Mark as completed
        localStorage.setItem("fundingHub_completed", "true")

        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500))

        setIsLoading(false)
        setShowSuccess(true)

        // Dispatch custom event to update sidebar
        window.dispatchEvent(new Event("formStatusUpdate"))

        // Auto-hide success message and redirect to dashboard (final submission for masters/phd)
        setTimeout(() => {
            setShowSuccess(false)
            // Show success message for final application submission
            alert("Application submitted successfully!")
            router.push("/student")
        }, 2000)
    }

    // Handle save (without completion)
    const handleSave = () => {
        saveData()
        // Show brief save confirmation
        const originalText = "Save"
        const saveButton = document.querySelector("[data-save-button]")
        if (saveButton) {
            saveButton.textContent = "Saved!"
            setTimeout(() => {
                saveButton.textContent = originalText
            }, 1000)
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
            {/* Success Overlay */}
            <AnimatePresence>
                {showSuccess && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm"
                    >
                        <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.8, opacity: 0 }}
                            className="bg-white rounded-2xl p-8 shadow-2xl border border-green-200"
                        >
                            <div className="text-center">
                                <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                                    className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center"
                                >
                                    <CheckCircle className="w-8 h-8 text-green-600" />
                                </motion.div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">Funding Hub Completed!</h3>
                                <p className="text-gray-600">Your funding information has been saved successfully.</p>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>

            <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                {/* <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl mb-4 shadow-lg">
                        <DollarSign className="w-8 h-8 text-white" />
                    </div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                        Funding Hub
                    </h1>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                        Explore funding opportunities, research positions, and assistantship programs to support your academic
                        journey.
                    </p>
                </motion.div> */}
                <Card>
                    <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg text-center">
                        <CardTitle className="flex flex-col items-center justify-center gap-3 text-xl">
                            <div className="p-2 bg-white/20 rounded-lg">
                                <DollarSign className="w-8 h-8 text-white" />
                            </div>
                            Funding Hub
                        </CardTitle>
                        <CardDescription className="text-blue-100">
                            Explore funding opportunities, research positions, and assistantship programs to support your academic
                            journey.
                        </CardDescription>
                    </CardHeader>

                    <CardContent className="p-6 sm:p-8">
                        <form onSubmit={handleSubmit} className="space-y-8">
                            {/* CV Upload Section */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-blue-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-blue-800">
                                            <Upload className="w-6 h-6" />
                                            Upload CV
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="border-2 border-dashed border-blue-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors">
                                            <input
                                                type="file"
                                                id="cv-upload"
                                                accept=".pdf,.doc,.docx"
                                                onChange={handleCvUpload}
                                                className="hidden"
                                            />
                                            <label htmlFor="cv-upload" className="cursor-pointer">
                                                <FileText className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                                                {cvFile ? (
                                                    <div>
                                                        <p className="text-blue-800 font-medium">{cvFile.name}</p>
                                                        <p className="text-blue-600 text-sm">Click to change file</p>
                                                    </div>
                                                ) : (
                                                    <div>
                                                        <p className="text-blue-800 font-medium mb-2">Choose File</p>
                                                        <p className="text-blue-600 text-sm">PDF, DOC, or DOCX format</p>
                                                    </div>
                                                )}
                                            </label>
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Research Topics Section */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-purple-50 to-purple-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-purple-800">
                                            <BookOpen className="w-6 h-6" />
                                            Research Topics of Interest
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex gap-3">
                                            <Input
                                                value={newResearchTopic}
                                                onChange={(e) => setNewResearchTopic(e.target.value)}
                                                placeholder="e.g., Machine Learning, Quantum Computing, Bioengineering..."
                                                className="flex-1 h-12 border-purple-200 focus:border-purple-400"
                                                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addResearchTopic())}
                                            />
                                            <Button
                                                type="button"
                                                onClick={addResearchTopic}
                                                className="h-12 px-6 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800"
                                            >
                                                <Plus className="w-4 h-4 mr-2" />
                                                Add
                                            </Button>
                                        </div>

                                        <AnimatePresence>
                                            {researchTopics.map((topic) => (
                                                <motion.div
                                                    key={topic.id}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    exit={{ opacity: 0, x: 20 }}
                                                    className="flex items-center justify-between bg-white p-4 rounded-lg border border-purple-200"
                                                >
                                                    <span className="text-gray-800">{topic.topic}</span>
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => removeResearchTopic(topic.id)}
                                                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                                    >
                                                        <X className="w-4 h-4" />
                                                    </Button>
                                                </motion.div>
                                            ))}
                                        </AnimatePresence>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Research Publications Section */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-green-50 to-green-100">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-green-800">
                                            <FileText className="w-6 h-6" />
                                            Research Publications
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-3">
                                            <Textarea
                                                value={newPublication}
                                                onChange={(e) => setNewPublication(e.target.value)}
                                                placeholder="Enter publication details (title, journal, year, etc.)"
                                                className="min-h-[100px] border-green-200 focus:border-green-400"
                                            />
                                            <Button
                                                type="button"
                                                onClick={addPublication}
                                                className="w-full h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                                            >
                                                <Plus className="w-4 h-4 mr-2" />
                                                Add Publication
                                            </Button>
                                        </div>

                                        <AnimatePresence>
                                            {publications.map((pub) => (
                                                <motion.div
                                                    key={pub.id}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    exit={{ opacity: 0, x: 20 }}
                                                    className="bg-white p-4 rounded-lg border border-green-200"
                                                >
                                                    <div className="flex justify-between items-start">
                                                        <p className="text-gray-800 flex-1 pr-4">{pub.publication}</p>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removePublication(pub.id)}
                                                            className="text-red-500 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                                                        >
                                                            <X className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </AnimatePresence>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Assistantship Option */}
                            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
                                <Card className="border-0 shadow-lg bg-gradient-to-r from-orange-50 to-orange-100">
                                    <CardContent className="pt-6">
                                        <div className="flex items-center space-x-3">
                                            <Checkbox
                                                id="assistantship"
                                                checked={optForAssistantship}
                                                onCheckedChange={(checked) => setOptForAssistantship(checked as boolean)}
                                                className="data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600"
                                            />
                                            <div className="flex items-center gap-3">
                                                <GraduationCap className="w-6 h-6 text-orange-600" />
                                                <Label htmlFor="assistantship" className="text-orange-800 font-medium cursor-pointer">
                                                    Opt for teaching/research assistantship
                                                </Label>
                                            </div>
                                        </div>
                                        <p className="text-orange-700 text-sm mt-2 ml-9">
                                            Check this option if you're interested in teaching or research assistant positions that can help fund
                                            your education.
                                        </p>
                                    </CardContent>
                                </Card>
                            </motion.div>

                            {/* Navigation Buttons */}
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5 }}
                                className="flex flex-col sm:flex-row gap-4 pt-6"
                            >
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => router.back()}
                                    className="flex-1 h-12 border-gray-300 hover:bg-gray-50"
                                >
                                    Previous
                                </Button>
                                <Button
                                    type="button"
                                    onClick={handleSave}
                                    data-save-button
                                    className="flex-1 h-12 bg-gray-600 hover:bg-gray-700 text-white"
                                >
                                    Save
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isLoading}
                                    className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                                >
                                    {isLoading ? (
                                        <>
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            Submitting...
                                        </>
                                    ) : (
                                        "Submit Application"
                                    )}
                                </Button>
                            </motion.div>
                        </form>
                    </CardContent>
                </Card>

            </div>
        </div>
    )
}
