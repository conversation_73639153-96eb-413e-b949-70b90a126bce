"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { format } from "date-fns"
import {
    CalendarIcon,
    AlertCircle,
    CheckCircle,
    User,
    Phone,
    MapPin,
    Globe,
    Mail,
    ChevronDown,
    Check,
    Loader2,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { countries, nationalities } from "@/lib/countries"

// Animation variants
const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
}

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.1,
        },
    },
}

interface PersonalInfoForm {
    firstName: string
    middleName: string
    lastName: string
    primaryPhone: string
    secondaryPhone: string
    email: string
    cityOfBirth: string
    stateOfBirth: string
    countryOfBirth: string
    dateOfBirth: Date | undefined
    gender: string
    maritalStatus: string
    nationality: string
    addressLine1: string
    addressLine2: string
    addressLine3: string
    city: string
    state: string
    country: string
    zipCode: string
}

interface FormErrors {
    [key: string]: string
}

export default function PersonalInfoPage() {
    const router = useRouter()
    const [date, setDate] = useState<Date>()
    const [formData, setFormData] = useState<PersonalInfoForm>({
        firstName: "",
        middleName: "",
        lastName: "",
        primaryPhone: "",
        secondaryPhone: "",
        email: "",
        cityOfBirth: "",
        stateOfBirth: "",
        countryOfBirth: "",
        dateOfBirth: undefined,
        gender: "",
        maritalStatus: "",
        nationality: "",
        addressLine1: "",
        addressLine2: "",
        addressLine3: "",
        city: "",
        state: "",
        country: "",
        zipCode: "",
    })

    const [errors, setErrors] = useState<FormErrors>({})
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [openCountryBirth, setOpenCountryBirth] = useState(false)
    const [openCountryAddress, setOpenCountryAddress] = useState(false)
    const [openNationality, setOpenNationality] = useState(false)

    // Calculate age based on date of birth
    const calculateAge = (birthDate: Date | string): number => {
        const birthDateObj = new Date(birthDate)
        const today = new Date()
        const age = today.getFullYear() - birthDateObj.getFullYear()
        const monthDiff = today.getMonth() - birthDateObj.getMonth()

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
            return age - 1
        }
        return age
    }

    // Validation function
    const validateForm = (): boolean => {
        const newErrors: FormErrors = {}

        // Required fields validation
        if (!formData.firstName.trim()) newErrors.firstName = "First name is required"
        if (!formData.lastName.trim()) newErrors.lastName = "Last name is required"
        if (!formData.primaryPhone.trim()) newErrors.primaryPhone = "Primary phone is required"
        if (!formData.email.trim()) newErrors.email = "Email is required"
        if (!formData.cityOfBirth.trim()) newErrors.cityOfBirth = "City of birth is required"
        if (!formData.stateOfBirth.trim()) newErrors.stateOfBirth = "State of birth is required"
        if (!formData.countryOfBirth.trim()) newErrors.countryOfBirth = "Country of birth is required"
        if (!formData.dateOfBirth) newErrors.dateOfBirth = "Date of birth is required"
        if (!formData.gender) newErrors.gender = "Gender is required"
        if (!formData.maritalStatus) newErrors.maritalStatus = "Marital status is required"
        if (!formData.nationality) newErrors.nationality = "Nationality is required"
        if (!formData.addressLine1.trim()) newErrors.addressLine1 = "Address line 1 is required"
        if (!formData.city.trim()) newErrors.city = "City is required"
        if (!formData.country) newErrors.country = "Country is required"
        if (!formData.zipCode.trim()) newErrors.zipCode = "Zip code is required"

        // Email validation
        if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = "Please enter a valid email address"
        }

        // Phone validation (basic)
        if (formData.primaryPhone && !/^\+?[\d\s\-$$$$]+$/.test(formData.primaryPhone)) {
            newErrors.primaryPhone = "Please enter a valid phone number"
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    // Handle input changes
    const handleInputChange = (field: keyof PersonalInfoForm, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }))
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: "" }))
        }
    }

    // Handle date change
    const handleDateChange = (selectedDate: Date | undefined) => {
        setDate(selectedDate)
        setFormData((prev) => ({ ...prev, dateOfBirth: selectedDate }))
        if (errors.dateOfBirth) {
            setErrors((prev) => ({ ...prev, dateOfBirth: "" }))
        }
    }

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!validateForm()) {
            return
        }

        setIsSubmitting(true)

        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 2000))

            // Save form data
            localStorage.setItem("personalInfo", JSON.stringify(formData))
            // Mark as completed
            localStorage.setItem("personalInfo_completed", "true")

            // Trigger storage event for sidebar update
            window.dispatchEvent(new Event("storage"))

            // Check if user needs guardian info based on age
            const age = formData.dateOfBirth ? calculateAge(formData.dateOfBirth) : 18

            if (age <= 17) {
                // Navigate to Guardian Info
                router.push("/student/application-form/guardian-info")
            } else {
                // Skip Guardian Info and go to Academic Background
                router.push("/student/application-form/academic-background")
            }
        } catch (error) {
            console.error("Error saving personal info:", error)
        } finally {
            setIsSubmitting(false)
        }
    }

    // Load saved data on component mount
    useEffect(() => {
        const savedData = localStorage.getItem("personalInfo")
        if (savedData) {
            const parsed = JSON.parse(savedData)
            setFormData(parsed)
            if (parsed.dateOfBirth) {
                setDate(new Date(parsed.dateOfBirth))
            }
        }
    }, [])

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
            <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
                {/* Modern Page Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="mb-8"
                >
                    <div className="text-center">
                        <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                            Personal Information
                        </h1>
                        <p className="mt-2 text-lg text-gray-600 max-w-2xl mx-auto">
                            Help us get to know you better by providing your personal details
                        </p>
                        <div className="mt-4 h-1 w-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mx-auto" />
                    </div>
                </motion.div>

                <motion.div initial="hidden" animate="visible" variants={staggerContainer} className="max-w-6xl mx-auto">
                    <Card className="border-none shadow-xl bg-white/80 backdrop-blur-sm">
                        <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
                            <CardTitle className="flex items-center gap-3 text-xl">
                                <div className="p-2 bg-white/20 rounded-lg">
                                    <User className="h-6 w-6" />
                                </div>
                                Personal Information Form
                            </CardTitle>
                            <CardDescription className="text-blue-100">
                                Fields marked with * are required. Please ensure all information is accurate.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-6 sm:p-8">
                            <form onSubmit={handleSubmit} className="space-y-8">
                                {/* Personal Details Section */}
                                <motion.div variants={fadeInUp} className="space-y-6">
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <User className="h-5 w-5 text-blue-600" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900">Basic Information</h3>
                                    </div>

                                    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">
                                                First Name *
                                            </Label>
                                            <Input
                                                id="firstName"
                                                value={formData.firstName}
                                                onChange={(e) => handleInputChange("firstName", e.target.value)}
                                                className={cn(
                                                    "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.firstName && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                )}
                                                placeholder="Enter your first name"
                                            />
                                            {errors.firstName && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.firstName}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="middleName" className="text-sm font-medium text-gray-700">
                                                Middle Name
                                            </Label>
                                            <Input
                                                id="middleName"
                                                value={formData.middleName}
                                                onChange={(e) => handleInputChange("middleName", e.target.value)}
                                                className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                                placeholder="Enter your middle name"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">
                                                Last Name *
                                            </Label>
                                            <Input
                                                id="lastName"
                                                value={formData.lastName}
                                                onChange={(e) => handleInputChange("lastName", e.target.value)}
                                                className={cn(
                                                    "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.lastName && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                )}
                                                placeholder="Enter your last name"
                                            />
                                            {errors.lastName && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.lastName}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Contact Information Section */}
                                <motion.div variants={fadeInUp} className="space-y-6">
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <div className="p-2 bg-green-100 rounded-lg">
                                            <Phone className="h-5 w-5 text-green-600" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900">Contact Information</h3>
                                    </div>

                                    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="primaryPhone" className="text-sm font-medium text-gray-700">
                                                Primary Phone *
                                            </Label>
                                            <div className="relative">
                                                <Phone className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                                <Input
                                                    id="primaryPhone"
                                                    value={formData.primaryPhone}
                                                    onChange={(e) => handleInputChange("primaryPhone", e.target.value)}
                                                    className={cn(
                                                        "h-11 pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                        errors.primaryPhone && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                    )}
                                                    placeholder="+****************"
                                                />
                                            </div>
                                            {errors.primaryPhone && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.primaryPhone}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="secondaryPhone" className="text-sm font-medium text-gray-700">
                                                Secondary Phone
                                            </Label>
                                            <div className="relative">
                                                <Phone className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                                <Input
                                                    id="secondaryPhone"
                                                    value={formData.secondaryPhone}
                                                    onChange={(e) => handleInputChange("secondaryPhone", e.target.value)}
                                                    className="h-11 pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                                    placeholder="+****************"
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                                                Email Address *
                                            </Label>
                                            <div className="relative">
                                                <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    value={formData.email}
                                                    onChange={(e) => handleInputChange("email", e.target.value)}
                                                    className={cn(
                                                        "h-11 pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                        errors.email && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                    )}
                                                    placeholder="<EMAIL>"
                                                />
                                            </div>
                                            {errors.email && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.email}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Birth Information Section */}
                                <motion.div variants={fadeInUp} className="space-y-6">
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <div className="p-2 bg-purple-100 rounded-lg">
                                            <Globe className="h-5 w-5 text-purple-600" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900">Birth Information</h3>
                                    </div>

                                    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="cityOfBirth" className="text-sm font-medium text-gray-700">
                                                City of Birth *
                                            </Label>
                                            <Input
                                                id="cityOfBirth"
                                                value={formData.cityOfBirth}
                                                onChange={(e) => handleInputChange("cityOfBirth", e.target.value)}
                                                className={cn(
                                                    "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.cityOfBirth && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                )}
                                                placeholder="Enter city"
                                            />
                                            {errors.cityOfBirth && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.cityOfBirth}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="stateOfBirth" className="text-sm font-medium text-gray-700">
                                                State of Birth *
                                            </Label>
                                            <Input
                                                id="stateOfBirth"
                                                value={formData.stateOfBirth}
                                                onChange={(e) => handleInputChange("stateOfBirth", e.target.value)}
                                                className={cn(
                                                    "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.stateOfBirth && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                )}
                                                placeholder="Enter state/province"
                                            />
                                            {errors.stateOfBirth && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.stateOfBirth}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium text-gray-700">Country of Birth *</Label>
                                            <Popover open={openCountryBirth} onOpenChange={setOpenCountryBirth}>
                                                <PopoverTrigger asChild>
                                                    <Button
                                                        variant="outline"
                                                        role="combobox"
                                                        aria-expanded={openCountryBirth}
                                                        className={cn(
                                                            "h-11 w-full justify-between border-gray-300 hover:border-gray-400",
                                                            !formData.countryOfBirth && "text-muted-foreground",
                                                            errors.countryOfBirth && "border-red-500",
                                                        )}
                                                    >
                                                        {formData.countryOfBirth
                                                            ? countries.find((country) => country.code === formData.countryOfBirth)?.name
                                                            : "Select country"}
                                                        <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                    </Button>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-full p-0" align="start">
                                                    <Command>
                                                        <CommandInput placeholder="Search countries..." />
                                                        <CommandList>
                                                            <CommandEmpty>No country found.</CommandEmpty>
                                                            <CommandGroup>
                                                                {countries.map((country) => (
                                                                    <CommandItem
                                                                        key={country.code}
                                                                        value={country.name}
                                                                        onSelect={() => {
                                                                            handleInputChange("countryOfBirth", country.code)
                                                                            setOpenCountryBirth(false)
                                                                        }}
                                                                    >
                                                                        <Check
                                                                            className={cn(
                                                                                "mr-2 h-4 w-4",
                                                                                formData.countryOfBirth === country.code ? "opacity-100" : "opacity-0",
                                                                            )}
                                                                        />
                                                                        {country.name}
                                                                    </CommandItem>
                                                                ))}
                                                            </CommandGroup>
                                                        </CommandList>
                                                    </Command>
                                                </PopoverContent>
                                            </Popover>
                                            {errors.countryOfBirth && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.countryOfBirth}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium text-gray-700">Date of Birth *</Label>
                                            <Popover>
                                                <PopoverTrigger asChild>
                                                    <Button
                                                        variant="outline"
                                                        className={cn(
                                                            "h-11 w-full justify-start text-left font-normal border-gray-300",
                                                            !date && "text-muted-foreground",
                                                            errors.dateOfBirth && "border-red-500",
                                                        )}
                                                    >
                                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                                        {date ? format(date, "PPP") : <span>Pick a date</span>}
                                                    </Button>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-auto p-0" align="start">
                                                    <Calendar
                                                        mode="single"
                                                        selected={date}
                                                        onSelect={handleDateChange}
                                                        initialFocus
                                                        fromYear={1950}
                                                        toYear={new Date().getFullYear()}
                                                        captionLayout="dropdown-buttons"
                                                        className="rounded-md border shadow"
                                                    />
                                                </PopoverContent>
                                            </Popover>
                                            {errors.dateOfBirth && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.dateOfBirth}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Personal Details Section */}
                                <motion.div variants={fadeInUp} className="space-y-6">
                                    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium text-gray-700">Gender *</Label>
                                            <Select value={formData.gender} onValueChange={(value) => handleInputChange("gender", value)}>
                                                <SelectTrigger className={cn("h-11 border-gray-300", errors.gender && "border-red-500")}>
                                                    <SelectValue placeholder="Select gender" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="male">Male</SelectItem>
                                                    <SelectItem value="female">Female</SelectItem>
                                                    <SelectItem value="other">Other</SelectItem>
                                                    <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.gender && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.gender}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium text-gray-700">Marital Status *</Label>
                                            <Select
                                                value={formData.maritalStatus}
                                                onValueChange={(value) => handleInputChange("maritalStatus", value)}
                                            >
                                                <SelectTrigger className={cn("h-11 border-gray-300", errors.maritalStatus && "border-red-500")}>
                                                    <SelectValue placeholder="Select status" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="single">Single</SelectItem>
                                                    <SelectItem value="married">Married</SelectItem>
                                                    <SelectItem value="divorced">Divorced</SelectItem>
                                                    <SelectItem value="widowed">Widowed</SelectItem>
                                                    <SelectItem value="separated">Separated</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.maritalStatus && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.maritalStatus}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium text-gray-700">Nationality *</Label>
                                            <Popover open={openNationality} onOpenChange={setOpenNationality}>
                                                <PopoverTrigger asChild>
                                                    <Button
                                                        variant="outline"
                                                        role="combobox"
                                                        aria-expanded={openNationality}
                                                        className={cn(
                                                            "h-11 w-full justify-between border-gray-300 hover:border-gray-400",
                                                            !formData.nationality && "text-muted-foreground",
                                                            errors.nationality && "border-red-500",
                                                        )}
                                                    >
                                                        {formData.nationality || "Select nationality"}
                                                        <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                    </Button>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-full p-0" align="start">
                                                    <Command>
                                                        <CommandInput placeholder="Search nationalities..." />
                                                        <CommandList>
                                                            <CommandEmpty>No nationality found.</CommandEmpty>
                                                            <CommandGroup>
                                                                {nationalities.map((nationality) => (
                                                                    <CommandItem
                                                                        key={nationality}
                                                                        value={nationality}
                                                                        onSelect={() => {
                                                                            handleInputChange("nationality", nationality)
                                                                            setOpenNationality(false)
                                                                        }}
                                                                    >
                                                                        <Check
                                                                            className={cn(
                                                                                "mr-2 h-4 w-4",
                                                                                formData.nationality === nationality ? "opacity-100" : "opacity-0",
                                                                            )}
                                                                        />
                                                                        {nationality}
                                                                    </CommandItem>
                                                                ))}
                                                            </CommandGroup>
                                                        </CommandList>
                                                    </Command>
                                                </PopoverContent>
                                            </Popover>
                                            {errors.nationality && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.nationality}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Address Section */}
                                <motion.div variants={fadeInUp} className="space-y-6">
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <div className="p-2 bg-orange-100 rounded-lg">
                                            <MapPin className="h-5 w-5 text-orange-600" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900">Address Information</h3>
                                    </div>

                                    <div className="grid gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="addressLine1" className="text-sm font-medium text-gray-700">
                                                Address Line 1 *
                                            </Label>
                                            <Input
                                                id="addressLine1"
                                                value={formData.addressLine1}
                                                onChange={(e) => handleInputChange("addressLine1", e.target.value)}
                                                className={cn(
                                                    "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.addressLine1 && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                )}
                                                placeholder="Street address, P.O. box, company name, c/o"
                                            />
                                            {errors.addressLine1 && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-4 w-4" />
                                                    {errors.addressLine1}
                                                </p>
                                            )}
                                        </div>

                                        <div className="grid gap-4 sm:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="addressLine2" className="text-sm font-medium text-gray-700">
                                                    Address Line 2
                                                </Label>
                                                <Input
                                                    id="addressLine2"
                                                    value={formData.addressLine2}
                                                    onChange={(e) => handleInputChange("addressLine2", e.target.value)}
                                                    className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                                    placeholder="Apartment, suite, unit, building, floor, etc."
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="addressLine3" className="text-sm font-medium text-gray-700">
                                                    Address Line 3
                                                </Label>
                                                <Input
                                                    id="addressLine3"
                                                    value={formData.addressLine3}
                                                    onChange={(e) => handleInputChange("addressLine3", e.target.value)}
                                                    className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                                    placeholder="Additional address information"
                                                />
                                            </div>
                                        </div>

                                        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                                            <div className="space-y-2">
                                                <Label className="text-sm font-medium text-gray-700">Country *</Label>
                                                <Popover open={openCountryAddress} onOpenChange={setOpenCountryAddress}>
                                                    <PopoverTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            role="combobox"
                                                            aria-expanded={openCountryAddress}
                                                            className={cn(
                                                                "h-11 w-full justify-between border-gray-300 hover:border-gray-400",
                                                                !formData.country && "text-muted-foreground",
                                                                errors.country && "border-red-500",
                                                            )}
                                                        >
                                                            {formData.country
                                                                ? countries.find((country) => country.code === formData.country)?.name
                                                                : "Select country"}
                                                            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                        </Button>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-full p-0" align="start">
                                                        <Command>
                                                            <CommandInput placeholder="Search countries..." />
                                                            <CommandList>
                                                                <CommandEmpty>No country found.</CommandEmpty>
                                                                <CommandGroup>
                                                                    {countries.map((country) => (
                                                                        <CommandItem
                                                                            key={country.code}
                                                                            value={country.name}
                                                                            onSelect={() => {
                                                                                handleInputChange("country", country.code)
                                                                                setOpenCountryAddress(false)
                                                                            }}
                                                                        >
                                                                            <Check
                                                                                className={cn(
                                                                                    "mr-2 h-4 w-4",
                                                                                    formData.country === country.code ? "opacity-100" : "opacity-0",
                                                                                )}
                                                                            />
                                                                            {country.name}
                                                                        </CommandItem>
                                                                    ))}
                                                                </CommandGroup>
                                                            </CommandList>
                                                        </Command>
                                                    </PopoverContent>
                                                </Popover>
                                                {errors.country && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-4 w-4" />
                                                        {errors.country}
                                                    </p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="city" className="text-sm font-medium text-gray-700">
                                                    City *
                                                </Label>
                                                <Input
                                                    id="city"
                                                    value={formData.city}
                                                    onChange={(e) => handleInputChange("city", e.target.value)}
                                                    className={cn(
                                                        "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                        errors.city && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                    )}
                                                    placeholder="Enter city"
                                                />
                                                {errors.city && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-4 w-4" />
                                                        {errors.city}
                                                    </p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="state" className="text-sm font-medium text-gray-700">
                                                    State/Province
                                                </Label>
                                                <Input
                                                    id="state"
                                                    value={formData.state}
                                                    onChange={(e) => handleInputChange("state", e.target.value)}
                                                    className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                                    placeholder="Enter state/province"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="zipCode" className="text-sm font-medium text-gray-700">
                                                    Zip/Postal Code *
                                                </Label>
                                                <Input
                                                    id="zipCode"
                                                    value={formData.zipCode}
                                                    onChange={(e) => handleInputChange("zipCode", e.target.value)}
                                                    className={cn(
                                                        "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                                                        errors.zipCode && "border-red-500 focus:border-red-500 focus:ring-red-500",
                                                    )}
                                                    placeholder="Enter zip/postal code"
                                                />
                                                {errors.zipCode && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-4 w-4" />
                                                        {errors.zipCode}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Age-based Guardian Info Alert */}
                                {formData.dateOfBirth && calculateAge(formData.dateOfBirth) <= 17 && (
                                    <motion.div variants={fadeInUp}>
                                        <Alert className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                                            <AlertCircle className="h-5 w-5 text-blue-600" />
                                            <AlertDescription className="text-blue-800">
                                                <strong>Guardian Information Required:</strong> Since you are under 18, you will need to provide
                                                guardian information in the next step for legal and emergency contact purposes.
                                            </AlertDescription>
                                        </Alert>
                                    </motion.div>
                                )}

                                {/* Submit Button */}
                                <motion.div variants={fadeInUp} className="flex justify-end pt-6 border-t border-gray-200">
                                    <Button
                                        type="submit"
                                        disabled={isSubmitting}
                                        className="min-w-[160px] h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                                    >
                                        {isSubmitting ? (
                                            <>
                                                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                                Saving...
                                            </>
                                        ) : (
                                            <>
                                                <CheckCircle className="mr-2 h-5 w-5" />
                                                Save & Continue
                                            </>
                                        )}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </div>
    )
}
