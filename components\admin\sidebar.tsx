"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON><PERSON>, Users, School, FileText, Settings, Bell, HelpCircle } from "lucide-react"

export function AdminSidebar() {
  const pathname = usePathname()

  const menuItems = [
    {
      title: "Dashboard",
      href: "/admin",
      icon: <PERSON><PERSON><PERSON>,
    },
    {
      title: "Students",
      href: "/admin/students",
      icon: Users,
    },
    {
      title: "Universities",
      href: "/admin/universities",
      icon: School,
    },
    {
      title: "Applications",
      href: "/admin/applications",
      icon: FileText,
    },
    {
      title: "Settings",
      href: "/admin/settings",
      icon: Settings,
    },
    {
      title: "Notifications",
      href: "/admin/notifications",
      icon: Bell,
    },
  ]

  const supportItems = [
    {
      title: "Help Center",
      href: "/admin/help",
      icon: HelpCircle,
    },
  ]

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(`${href}/`)
  }

  return (
    <aside className="w-64 border-r bg-white">
      <nav className="flex h-full flex-col">
        <ul className="space-y-1 p-4">
          {menuItems.map((item) => (
            <li key={item.title}>
              <Link
                href={item.href}
                className={`flex items-center gap-3 rounded-md px-3 py-2 transition-colors ${
                  isActive(item.href) ? "bg-purple-100 text-purple-700" : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.title}</span>
              </Link>
            </li>
          ))}
        </ul>

        <div className="mt-auto">
          <ul className="space-y-1 border-t p-4">
            {supportItems.map((item) => (
              <li key={item.title}>
                <Link
                  href={item.href}
                  className={`flex items-center gap-3 rounded-md px-3 py-2 transition-colors ${
                    isActive(item.href) ? "bg-purple-100 text-purple-700" : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  <span>{item.title}</span>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>
    </aside>
  )
}
