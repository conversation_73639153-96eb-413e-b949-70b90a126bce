"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Heart } from "lucide-react"

interface FormData {
    hasDisabilities: boolean
    disabilityDetails: string
    requiresAccommodations: boolean
    accommodationDetails: string
    hasChronicIllness: boolean
    illnessDetails: string
}

export default function HealthInfoPage() {
    const router = useRouter()
    const [formData, setFormData] = useState<FormData>({
        hasDisabilities: false,
        disabilityDetails: "",
        requiresAccommodations: false,
        accommodationDetails: "",
        hasChronicIllness: false,
        illnessDetails: "",
    })
    const [isLoading, setIsLoading] = useState(false)
    const [userData, setUserData] = useState<{ applicationType?: string } | null>(null)

    useEffect(() => {
        const saved = localStorage.getItem("healthInfo_data")
        if (saved) {
            setFormData(JSON.parse(saved))
        }

        // Load user data to determine application type
        const userDataStr = localStorage.getItem("user-data")
        if (userDataStr) {
            setUserData(JSON.parse(userDataStr))
        }
    }, [])

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)

        localStorage.setItem("healthInfo_data", JSON.stringify(formData))
        localStorage.setItem("healthInfo_completed", "true")

        window.dispatchEvent(new Event("formStatusUpdate"))

        await new Promise((resolve) => setTimeout(resolve, 1000))
        setIsLoading(false)

        // Route to the correct next step based on application type
        if (userData?.applicationType === "athlete") {
            router.push("/student/application-form/athlete-info")
        } else if (
            userData?.applicationType === "masters" ||
            userData?.applicationType === "phd"
        ) {
            router.push("/student/application-form/funding-hub")
        } else {
            router.push("/student/application-form/additional-info")
        }
    }

    const handlePrevious = () => {
        localStorage.setItem("healthInfo_data", JSON.stringify(formData))
        router.push("/student/application-form/work-experience")
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            <div className="container mx-auto px-4 py-8">
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
                    <Card className="max-w-6xl mx-auto border-none shadow-xl bg-white/80 backdrop-blur-sm">

                        <CardHeader className="text-center pb-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm"></div>
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                                className="relative mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-6 backdrop-blur-sm"
                            >
                                <Heart className="w-8 h-8 text-white" />
                            </motion.div>
                            <CardTitle className="text-4xl font-bold relative">Health Info </CardTitle>
                            <p className="text-purple-100 mt-2 relative">
                                Please provide any relevant information about your health and any accommodations you may require.
                            </p>
                        </CardHeader>


                        <CardContent className="space-y-8 pt-2">
                            <form onSubmit={handleSubmit} className="space-y-8">
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                    className="grid md:grid-cols-2 gap-8"
                                >
                                    {/* Physical or Learning Disabilities */}
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-3">
                                            <Switch
                                                id="disabilities"
                                                checked={formData.hasDisabilities}
                                                onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, hasDisabilities: checked }))}
                                                className="data-[state=checked]:bg-blue-600"
                                            />
                                            <Label htmlFor="disabilities" className="text-sm font-medium text-gray-700">
                                                Have physical or learning disabilities
                                            </Label>
                                        </div>

                                        {formData.hasDisabilities && (
                                            <motion.div
                                                initial={{ opacity: 0, height: 0 }}
                                                animate={{ opacity: 1, height: "auto" }}
                                                exit={{ opacity: 0, height: 0 }}
                                                transition={{ duration: 0.3 }}
                                                className="space-y-2"
                                            >
                                                <Label htmlFor="disabilityDetails" className="text-sm font-medium text-gray-700">
                                                    Please provide details
                                                </Label>
                                                <Textarea
                                                    id="disabilityDetails"
                                                    value={formData.disabilityDetails}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, disabilityDetails: e.target.value }))}
                                                    className="min-h-[120px] border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                                    placeholder="Please describe your physical or learning disabilities..."
                                                />
                                            </motion.div>
                                        )}
                                    </div>

                                    {/* Special Accommodations */}
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-3">
                                            <Switch
                                                id="accommodations"
                                                checked={formData.requiresAccommodations}
                                                onCheckedChange={(checked) =>
                                                    setFormData((prev) => ({ ...prev, requiresAccommodations: checked }))
                                                }
                                                className="data-[state=checked]:bg-blue-600"
                                            />
                                            <Label htmlFor="accommodations" className="text-sm font-medium text-gray-700">
                                                Require special accommodations for studies
                                            </Label>
                                        </div>

                                        {formData.requiresAccommodations && (
                                            <motion.div
                                                initial={{ opacity: 0, height: 0 }}
                                                animate={{ opacity: 1, height: "auto" }}
                                                exit={{ opacity: 0, height: 0 }}
                                                transition={{ duration: 0.3 }}
                                                className="space-y-2"
                                            >
                                                <Label htmlFor="accommodationDetails" className="text-sm font-medium text-gray-700">
                                                    Please provide details
                                                </Label>
                                                <Textarea
                                                    id="accommodationDetails"
                                                    value={formData.accommodationDetails}
                                                    onChange={(e) => setFormData((prev) => ({ ...prev, accommodationDetails: e.target.value }))}
                                                    className="min-h-[120px] border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                                    placeholder="Please describe the accommodations you require..."
                                                />
                                            </motion.div>
                                        )}
                                    </div>
                                </motion.div>

                                {/* Chronic Illnesses */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                    className="space-y-4"
                                >
                                    <div className="flex items-center space-x-3">
                                        <Switch
                                            id="chronicIllness"
                                            checked={formData.hasChronicIllness}
                                            onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, hasChronicIllness: checked }))}
                                            className="data-[state=checked]:bg-blue-600"
                                        />
                                        <Label htmlFor="chronicIllness" className="text-sm font-medium text-gray-700">
                                            Have chronic illnesses or medical conditions
                                        </Label>
                                    </div>

                                    {formData.hasChronicIllness && (
                                        <motion.div
                                            initial={{ opacity: 0, height: 0 }}
                                            animate={{ opacity: 1, height: "auto" }}
                                            exit={{ opacity: 0, height: 0 }}
                                            transition={{ duration: 0.3 }}
                                            className="space-y-2"
                                        >
                                            <Label htmlFor="illnessDetails" className="text-sm font-medium text-gray-700">
                                                Please provide details
                                            </Label>
                                            <Textarea
                                                id="illnessDetails"
                                                value={formData.illnessDetails}
                                                onChange={(e) => setFormData((prev) => ({ ...prev, illnessDetails: e.target.value }))}
                                                className="min-h-[120px] border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                                placeholder="Please describe your chronic illnesses or medical conditions..."
                                            />
                                        </motion.div>
                                    )}
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.3 }}
                                    className="flex gap-4 pt-8"
                                >
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handlePrevious}
                                        className="flex-1 h-12 text-gray-600 border-gray-300 hover:bg-gray-50"
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                                    >
                                        {isLoading ? "Saving..." : "Save"}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </div>
    )
}
