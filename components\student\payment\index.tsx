"use client"

import { useState } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
    AlertCircle,
    ArrowRight,
    CheckCircle,
    CreditCard,
    Download,
    FileText,
    Gift,
    HelpCircle,
    Info,
    Landmark,
    Receipt,
    Shield,
    Tag,
    Wallet,
} from "lucide-react"

// Animation variants
const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
}

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
        },
    },
}

export default function PaymentPageComponent() {
    const [ref, inView] = useInView({
        triggerOnce: true,
        threshold: 0.1,
    })

    const [activeTab, setActiveTab] = useState("payment")
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("credit-card")
    const [promoCode, setPromoCode] = useState("")
    const [promoApplied, setPromoApplied] = useState(false)

    // Mock payment history data
    const paymentHistory = [
        {
            id: "INV-2025-001",
            date: "May 15, 2025",
            amount: "$250.00",
            status: "paid",
            description: "Application Processing Fee",
        },
        {
            id: "INV-2025-002",
            date: "April 10, 2025",
            amount: "$150.00",
            status: "paid",
            description: "Document Verification Fee",
        },
        {
            id: "INV-2025-003",
            date: "March 5, 2025",
            amount: "$50.00",
            status: "paid",
            description: "Express Processing Fee",
        },
    ]

    // Handle promo code application
    const handleApplyPromo = () => {
        if (promoCode.trim() !== "") {
            setPromoApplied(true)
        }
    }

    return (
        <div className="container mx-auto p-10">
            {/* Page Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900">Payment Center</h1>
                <p className="text-gray-600">Manage your payments and view transaction history</p>
            </div>

            {/* Main Content */}
            <Tabs defaultValue="payment" value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="mb-6 grid w-full max-w-md grid-cols-2">
                    <TabsTrigger
                        value="payment"
                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                    >
                        <CreditCard className="mr-2 h-4 w-4" />
                        Make Payment
                    </TabsTrigger>
                    <TabsTrigger
                        value="history"
                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                    >
                        <Receipt className="mr-2 h-4 w-4" />
                        Payment History
                    </TabsTrigger>
                </TabsList>

                {/* Payment Tab */}
                <TabsContent value="payment">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Left Column - Payment Options */}
                        <motion.div
                            ref={ref}
                            initial="hidden"
                            animate={inView ? "visible" : "hidden"}
                            variants={fadeInUp}
                            className="md:col-span-2"
                        >
                            <Card className="border-none shadow-md">
                                <CardHeader>
                                    <CardTitle>Make a Payment</CardTitle>
                                    <CardDescription>Select a payment method and enter your details</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    {/* Payment Summary */}
                                    <div className="rounded-lg bg-gray-50 p-4">
                                        <h3 className="mb-3 font-medium text-gray-900">Payment Summary</h3>
                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <span className="text-gray-600">Application Processing Fee</span>
                                                <span className="font-medium">$250.00</span>
                                            </div>
                                            <div className="flex items-center justify-between">
                                                <span className="text-gray-600">Service Fee</span>
                                                <span className="font-medium">$5.00</span>
                                            </div>
                                            {promoApplied && (
                                                <div className="flex items-center justify-between text-green-600">
                                                    <span className="flex items-center">
                                                        <Tag className="mr-1 h-4 w-4" />
                                                        Promo Discount (WELCOME25)
                                                    </span>
                                                    <span className="font-medium">-$25.00</span>
                                                </div>
                                            )}
                                            <div className="border-t border-gray-200 pt-2">
                                                <div className="flex items-center justify-between font-bold">
                                                    <span>Total Amount</span>
                                                    <span className="text-lg">{promoApplied ? "$230.00" : "$255.00"}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Payment Methods */}
                                    <div className="space-y-4">
                                        <h3 className="font-medium text-gray-900">Select Payment Method</h3>
                                        <div className="grid gap-4 md:grid-cols-3">
                                            <div
                                                className={`flex cursor-pointer flex-col items-center rounded-lg border p-4 transition-all hover:border-purple-200 hover:bg-purple-50 ${selectedPaymentMethod === "credit-card" ? "border-purple-500 bg-purple-50" : "border-gray-200"
                                                    }`}
                                                onClick={() => setSelectedPaymentMethod("credit-card")}
                                            >
                                                <div
                                                    className={`mb-2 rounded-full p-2 ${selectedPaymentMethod === "credit-card"
                                                            ? "bg-purple-100 text-purple-600"
                                                            : "bg-gray-100 text-gray-500"
                                                        }`}
                                                >
                                                    <CreditCard className="h-6 w-6" />
                                                </div>
                                                <span className="text-sm font-medium">Credit Card</span>
                                                <div className="mt-2 flex gap-1">
                                                    <Image
                                                        src="/placeholder.svg?height=20&width=30&text=Visa"
                                                        alt="Visa"
                                                        width={30}
                                                        height={20}
                                                        className="h-5 w-auto"
                                                    />
                                                    <Image
                                                        src="/placeholder.svg?height=20&width=30&text=MC"
                                                        alt="Mastercard"
                                                        width={30}
                                                        height={20}
                                                        className="h-5 w-auto"
                                                    />
                                                </div>
                                            </div>

                                            <div
                                                className={`flex cursor-pointer flex-col items-center rounded-lg border p-4 transition-all hover:border-purple-200 hover:bg-purple-50 ${selectedPaymentMethod === "bank-transfer"
                                                        ? "border-purple-500 bg-purple-50"
                                                        : "border-gray-200"
                                                    }`}
                                                onClick={() => setSelectedPaymentMethod("bank-transfer")}
                                            >
                                                <div
                                                    className={`mb-2 rounded-full p-2 ${selectedPaymentMethod === "bank-transfer"
                                                            ? "bg-purple-100 text-purple-600"
                                                            : "bg-gray-100 text-gray-500"
                                                        }`}
                                                >
                                                    <Landmark className="h-6 w-6" />
                                                </div>
                                                <span className="text-sm font-medium">Bank Transfer</span>
                                                <span className="mt-2 text-xs text-gray-500">ACH/Wire</span>
                                            </div>

                                            <div
                                                className={`flex cursor-pointer flex-col items-center rounded-lg border p-4 transition-all hover:border-purple-200 hover:bg-purple-50 ${selectedPaymentMethod === "digital-wallet"
                                                        ? "border-purple-500 bg-purple-50"
                                                        : "border-gray-200"
                                                    }`}
                                                onClick={() => setSelectedPaymentMethod("digital-wallet")}
                                            >
                                                <div
                                                    className={`mb-2 rounded-full p-2 ${selectedPaymentMethod === "digital-wallet"
                                                            ? "bg-purple-100 text-purple-600"
                                                            : "bg-gray-100 text-gray-500"
                                                        }`}
                                                >
                                                    <Wallet className="h-6 w-6" />
                                                </div>
                                                <span className="text-sm font-medium">Digital Wallet</span>
                                                <div className="mt-2 flex gap-1">
                                                    <Image
                                                        src="/placeholder.svg?height=20&width=30&text=PP"
                                                        alt="PayPal"
                                                        width={30}
                                                        height={20}
                                                        className="h-5 w-auto"
                                                    />
                                                    <Image
                                                        src="/placeholder.svg?height=20&width=30&text=AP"
                                                        alt="Apple Pay"
                                                        width={30}
                                                        height={20}
                                                        className="h-5 w-auto"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Payment Form - Credit Card */}
                                    {selectedPaymentMethod === "credit-card" && (
                                        <div className="space-y-4 rounded-lg border border-gray-200 p-4">
                                            <h3 className="font-medium text-gray-900">Credit Card Details</h3>
                                            <div className="space-y-4">
                                                <div>
                                                    <label htmlFor="card-name" className="mb-1 block text-sm font-medium text-gray-700">
                                                        Cardholder Name
                                                    </label>
                                                    <Input id="card-name" placeholder="Name as it appears on card" />
                                                </div>
                                                <div>
                                                    <label htmlFor="card-number" className="mb-1 block text-sm font-medium text-gray-700">
                                                        Card Number
                                                    </label>
                                                    <Input id="card-number" placeholder="•••• •••• •••• ••••" />
                                                </div>
                                                <div className="grid grid-cols-2 gap-4">
                                                    <div>
                                                        <label htmlFor="expiry" className="mb-1 block text-sm font-medium text-gray-700">
                                                            Expiry Date
                                                        </label>
                                                        <Input id="expiry" placeholder="MM/YY" />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="cvv" className="mb-1 block text-sm font-medium text-gray-700">
                                                            CVV
                                                        </label>
                                                        <Input id="cvv" placeholder="•••" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Payment Form - Bank Transfer */}
                                    {selectedPaymentMethod === "bank-transfer" && (
                                        <div className="space-y-4 rounded-lg border border-gray-200 p-4">
                                            <h3 className="font-medium text-gray-900">Bank Transfer Details</h3>
                                            <Alert className="bg-blue-50">
                                                <Info className="h-4 w-4 text-blue-600" />
                                                <AlertDescription className="text-blue-700">
                                                    Please use the following details to make your bank transfer. Include your Student ID in the
                                                    reference.
                                                </AlertDescription>
                                            </Alert>
                                            <div className="space-y-2 rounded-lg bg-gray-50 p-4">
                                                <div className="flex justify-between">
                                                    <span className="text-sm text-gray-600">Bank Name:</span>
                                                    <span className="text-sm font-medium">International Education Bank</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-sm text-gray-600">Account Name:</span>
                                                    <span className="text-sm font-medium">CLA 360 Education Services</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-sm text-gray-600">Account Number:</span>
                                                    <span className="text-sm font-medium">**********</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-sm text-gray-600">Routing Number:</span>
                                                    <span className="text-sm font-medium">*********</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-sm text-gray-600">SWIFT/BIC:</span>
                                                    <span className="text-sm font-medium">IEDUUS12</span>
                                                </div>
                                            </div>
                                            <div className="flex justify-end">
                                                <Button variant="outline" size="sm">
                                                    <Download className="mr-2 h-4 w-4" />
                                                    Download Instructions
                                                </Button>
                                            </div>
                                        </div>
                                    )}

                                    {/* Payment Form - Digital Wallet */}
                                    {selectedPaymentMethod === "digital-wallet" && (
                                        <div className="space-y-4 rounded-lg border border-gray-200 p-4">
                                            <h3 className="font-medium text-gray-900">Digital Wallet</h3>
                                            <div className="flex flex-col items-center justify-center space-y-4 py-4">
                                                <div className="grid grid-cols-2 gap-4">
                                                    <Button className="flex h-auto flex-col items-center gap-2 bg-blue-500 p-4 hover:bg-blue-600">
                                                        <Image
                                                            src="/placeholder.svg?height=40&width=40&text=PP"
                                                            alt="PayPal"
                                                            width={40}
                                                            height={40}
                                                            className="h-10 w-10 rounded-full bg-white p-1"
                                                        />
                                                        <span>Pay with PayPal</span>
                                                    </Button>
                                                    <Button className="flex h-auto flex-col items-center gap-2 bg-black p-4 hover:bg-gray-800">
                                                        <Image
                                                            src="/placeholder.svg?height=40&width=40&text=AP"
                                                            alt="Apple Pay"
                                                            width={40}
                                                            height={40}
                                                            className="h-10 w-10 rounded-full bg-white p-1"
                                                        />
                                                        <span>Pay with Apple Pay</span>
                                                    </Button>
                                                </div>
                                                <p className="text-center text-sm text-gray-500">
                                                    You will be redirected to the selected payment provider to complete your payment.
                                                </p>
                                            </div>
                                        </div>
                                    )}

                                    {/* Billing Address */}
                                    <div className="space-y-4">
                                        <h3 className="font-medium text-gray-900">Billing Address</h3>
                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div>
                                                <label htmlFor="country" className="mb-1 block text-sm font-medium text-gray-700">
                                                    Country
                                                </label>
                                                <Input id="country" placeholder="Country" />
                                            </div>
                                            <div>
                                                <label htmlFor="postal-code" className="mb-1 block text-sm font-medium text-gray-700">
                                                    Postal Code
                                                </label>
                                                <Input id="postal-code" placeholder="Postal Code" />
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                                <CardFooter className="flex justify-between border-t border-gray-100 bg-gray-50 px-6 py-4">
                                    <Button variant="outline">Cancel</Button>
                                    <Button className="bg-purple-600 hover:bg-purple-700">
                                        <Shield className="mr-2 h-4 w-4" />
                                        Pay Securely {promoApplied ? "$230.00" : "$255.00"}
                                    </Button>
                                </CardFooter>
                            </Card>
                        </motion.div>

                        {/* Right Column - Promo Code and Info */}
                        <motion.div
                            initial="hidden"
                            animate={inView ? "visible" : "hidden"}
                            variants={fadeInUp}
                            className="md:col-span-1"
                        >
                            {/* Promo Code */}
                            <Card className="border-none shadow-md">
                                <CardHeader className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                                    <CardTitle className="flex items-center gap-2">
                                        <Gift className="h-5 w-5" />
                                        Apply Promo Code
                                    </CardTitle>
                                    <CardDescription className="text-blue-100">
                                        Enter your promo code to receive a discount
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <div className="space-y-4">
                                        {promoApplied ? (
                                            <div className="rounded-lg bg-green-50 p-4 text-green-800">
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                                    <div>
                                                        <p className="font-medium">Promo code applied!</p>
                                                        <p className="text-sm">You saved $25.00 with code WELCOME25</p>
                                                    </div>
                                                </div>
                                            </div>
                                        ) : (
                                            <>
                                                <div className="flex space-x-2">
                                                    <Input
                                                        placeholder="Enter promo code"
                                                        value={promoCode}
                                                        onChange={(e) => setPromoCode(e.target.value)}
                                                    />
                                                    <Button
                                                        className="bg-purple-600 hover:bg-purple-700"
                                                        onClick={handleApplyPromo}
                                                        disabled={!promoCode.trim()}
                                                    >
                                                        Apply
                                                    </Button>
                                                </div>
                                                <p className="text-sm text-gray-500">
                                                    Enter a valid promo code to receive a discount on your payment.
                                                </p>
                                            </>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Payment Information */}
                            <motion.div initial="hidden" animate={inView ? "visible" : "hidden"} variants={fadeInUp} className="mt-6">
                                <Card className="border-none shadow-md">
                                    <CardHeader className="pb-2">
                                        <CardTitle className="text-lg">Payment Information</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            <Alert className="bg-amber-50">
                                                <AlertCircle className="h-4 w-4 text-amber-600" />
                                                <AlertDescription className="text-amber-700">
                                                    A $5 USD service fee will be applied to all payments. Promo codes may offer discounts.
                                                </AlertDescription>
                                            </Alert>

                                            <div className="rounded-lg border border-gray-100 p-4">
                                                <h4 className="mb-2 font-medium text-gray-900">What am I paying for?</h4>
                                                <p className="text-sm text-gray-600">
                                                    This payment covers your application processing fee, which includes document verification and
                                                    application review services.
                                                </p>
                                            </div>

                                            <div className="rounded-lg border border-gray-100 p-4">
                                                <h4 className="mb-2 font-medium text-gray-900">Secure Payment</h4>
                                                <p className="text-sm text-gray-600">
                                                    All payments are processed securely. Your financial information is encrypted and never stored
                                                    on our servers.
                                                </p>
                                                <div className="mt-2 flex items-center gap-2">
                                                    <Shield className="h-4 w-4 text-green-600" />
                                                    <span className="text-xs text-green-600">256-bit SSL Encryption</span>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                    <CardFooter className="border-t border-gray-100 p-4">
                                        <Button variant="ghost" className="w-full justify-center text-purple-600 hover:text-purple-700">
                                            <HelpCircle className="mr-2 h-4 w-4" />
                                            Need Help with Payment?
                                        </Button>
                                    </CardFooter>
                                </Card>
                            </motion.div>
                        </motion.div>
                    </div>
                </TabsContent>

                {/* History Tab */}
                <TabsContent value="history">
                    <div className="grid gap-6 md:grid-cols-3">
                        <div className="md:col-span-3">
                            <Card className="border-none shadow-md">
                                <CardHeader>
                                    <CardTitle>Payment History</CardTitle>
                                    <CardDescription>View your past transactions and download receipts</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="overflow-x-auto">
                                        <table className="w-full">
                                            <thead>
                                                <tr className="border-b text-left">
                                                    <th className="pb-3 pr-4 font-medium">Invoice</th>
                                                    <th className="pb-3 pr-4 font-medium">Date</th>
                                                    <th className="pb-3 pr-4 font-medium">Description</th>
                                                    <th className="pb-3 pr-4 font-medium">Amount</th>
                                                    <th className="pb-3 pr-4 font-medium">Status</th>
                                                    <th className="pb-3 pr-4 font-medium">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {paymentHistory.map((payment) => (
                                                    <tr key={payment.id} className="border-b">
                                                        <td className="py-3 pr-4">{payment.id}</td>
                                                        <td className="py-3 pr-4">{payment.date}</td>
                                                        <td className="py-3 pr-4">{payment.description}</td>
                                                        <td className="py-3 pr-4 font-medium">{payment.amount}</td>
                                                        <td className="py-3 pr-4">
                                                            <Badge
                                                                className={
                                                                    payment.status === "paid"
                                                                        ? "bg-green-100 text-green-800"
                                                                        : "bg-amber-100 text-amber-800"
                                                                }
                                                            >
                                                                {payment.status === "paid" ? "Paid" : "Pending"}
                                                            </Badge>
                                                        </td>
                                                        <td className="py-3 pr-4">
                                                            <Button variant="ghost" size="sm" className="h-8 text-gray-600 hover:text-gray-900">
                                                                <FileText className="mr-1 h-4 w-4" />
                                                                Receipt
                                                            </Button>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>

                                    {paymentHistory.length === 0 && (
                                        <div className="flex flex-col items-center justify-center py-12 text-center">
                                            <div className="mb-4 rounded-full bg-gray-100 p-3">
                                                <Receipt className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <h3 className="mb-1 text-lg font-medium">No payment history</h3>
                                            <p className="mb-4 text-gray-500">You haven't made any payments yet.</p>
                                        </div>
                                    )}
                                </CardContent>
                                <CardFooter className="border-t border-gray-100 bg-gray-50 p-4">
                                    <div className="flex w-full items-center justify-between">
                                        <Button variant="outline" size="sm">
                                            <Download className="mr-2 h-4 w-4" />
                                            Export All
                                        </Button>
                                        <p className="text-sm text-gray-500">
                                            Showing {paymentHistory.length} of {paymentHistory.length} transactions
                                        </p>
                                    </div>
                                </CardFooter>
                            </Card>
                        </div>
                    </div>
                </TabsContent>
            </Tabs>

            {/* Need Help Section */}
            <motion.div
                initial="hidden"
                animate={inView ? "visible" : "hidden"}
                variants={fadeInUp}
                className="mt-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm"
            >
                <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
                    <div className="flex items-start gap-4">
                        <div className="rounded-full bg-purple-100 p-3 text-purple-600">
                            <HelpCircle className="h-6 w-6" />
                        </div>
                        <div>
                            <h3 className="text-lg font-bold">Need help with your payment?</h3>
                            <p className="text-gray-600">
                                Our support team is available 24/7 to assist you with any payment-related questions.
                            </p>
                        </div>
                    </div>
                    <Button className="whitespace-nowrap bg-purple-600 hover:bg-purple-700">
                        <ArrowRight className="mr-2 h-4 w-4" />
                        Contact Support
                    </Button>
                </div>
            </motion.div>
        </div>
    )
}
