import type React from "react"
import Link from "next/link"
import Image from "next/image"
import { StudentSidebar } from "@/components/student/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Menu } from "lucide-react"
import StudentHeader from "@/components/student/header"

export default function StudentLayout({
    children,
}: {
    children: React.ReactNode
}) {
    return (
        <div className="flex min-h-screen bg-gray-50">
            <StudentHeader />
            <StudentSidebar />

            {/* Mobile header */}
            <header className="fixed inset-x-0 top-0 z-30 flex h-16 items-center justify-between border-b border-gray-200 bg-white px-4 md:hidden">
                <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" className="md:hidden">
                        <Menu className="h-5 w-5" />
                    </Button>
                    <Link href="/student" className="flex items-center gap-2">
                        <div className="relative h-8 w-8 overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-blue-500">
                            <Image
                                src="/placeholder.svg"
                                alt="CLA 360 Logo"
                                width={32}
                                height={32}
                                className="h-8 w-8 object-cover"
                            />
                        </div>
                        <span className="text-lg font-bold text-gray-900">CLA 360</span>
                    </Link>
                </div>
                <div className="relative h-8 w-8 overflow-hidden rounded-full">
                    <Image src="/placeholder.svg" alt="User" width={32} height={32} className="h-8 w-8 object-cover" />
                </div>
            </header>

            <main className="flex-1 md:ml-64">
                <div className="pt-16 md:pt-0">{children}</div>
            </main>
        </div>
    )
}
