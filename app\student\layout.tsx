import type React from "react"
import { StudentSidebar } from "@/components/student/sidebar"
import StudentHeader from "@/components/student/header"

export default function StudentLayout({
    children,
}: {
    children: React.ReactNode
}) {
    return (
        <div className="flex min-h-screen bg-gray-50">
            <StudentSidebar />

            <div className="flex flex-1 flex-col md:ml-64">
                <StudentHeader />
                <main className="flex-1">
                    {children}
                </main>
            </div>
        </div>
    )
}
