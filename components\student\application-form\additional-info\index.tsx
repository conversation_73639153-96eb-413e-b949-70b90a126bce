"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { FileText, Users } from "lucide-react"
import { cn } from "@/lib/utils"

interface Reference {
    title: string
    name: string
    organization: string
    email: string
    phone: string
}

interface FormData {
    personalStatement: string
    reference1: Reference
    reference2: Reference
    hasCriminalRecord: boolean
    hasBeenExpelled: boolean
    howDidYouHear: string
}

export default function AdditionalInfoPage() {
    const router = useRouter()
    const [formData, setFormData] = useState<FormData>({
        personalStatement: "",
        reference1: { title: "", name: "", organization: "", email: "", phone: "" },
        reference2: { title: "", name: "", organization: "", email: "", phone: "" },
        hasCriminalRecord: false,
        hasBeenExpelled: false,
        howDidYouHear: "",
    })
    const [errors, setErrors] = useState<Record<string, string>>({})
    const [isLoading, setIsLoading] = useState(false)
    const [wordCount, setWordCount] = useState(0)

    useEffect(() => {
        const saved = localStorage.getItem("additionalInfo_data")
        if (saved) {
            setFormData(JSON.parse(saved))
        }
    }, [])

    useEffect(() => {
        const words = formData.personalStatement
            .trim()
            .split(/\s+/)
            .filter((word) => word.length > 0)
        setWordCount(words.length)
    }, [formData.personalStatement])

    const validateForm = () => {
        const newErrors: Record<string, string> = {}

        if (!formData.personalStatement.trim()) {
            newErrors.personalStatement = "Personal statement is required"
        } else if (wordCount < 500) {
            newErrors.personalStatement = "Personal statement must be at least 500 words"
        } else if (wordCount > 1000) {
            newErrors.personalStatement = "Personal statement must not exceed 1000 words"
        }

        // Reference 1 validation
        if (!formData.reference1.title) {
            newErrors.reference1Title = "Reference 1 title is required"
        }
        if (!formData.reference1.name.trim()) {
            newErrors.reference1Name = "Reference 1 name is required"
        }
        if (!formData.reference1.organization.trim()) {
            newErrors.reference1Organization = "Reference 1 organization is required"
        }
        if (!formData.reference1.email.trim()) {
            newErrors.reference1Email = "Reference 1 email is required"
        } else if (!/\S+@\S+\.\S+/.test(formData.reference1.email)) {
            newErrors.reference1Email = "Please enter a valid email address"
        }
        if (!formData.reference1.phone.trim()) {
            newErrors.reference1Phone = "Reference 1 phone is required"
        }

        // Reference 2 validation
        if (!formData.reference2.title) {
            newErrors.reference2Title = "Reference 2 title is required"
        }
        if (!formData.reference2.name.trim()) {
            newErrors.reference2Name = "Reference 2 name is required"
        }
        if (!formData.reference2.organization.trim()) {
            newErrors.reference2Organization = "Reference 2 organization is required"
        }
        if (!formData.reference2.email.trim()) {
            newErrors.reference2Email = "Reference 2 email is required"
        } else if (!/\S+@\S+\.\S+/.test(formData.reference2.email)) {
            newErrors.reference2Email = "Please enter a valid email address"
        }
        if (!formData.reference2.phone.trim()) {
            newErrors.reference2Phone = "Reference 2 phone is required"
        }

        if (!formData.howDidYouHear) {
            newErrors.howDidYouHear = "Please tell us how you heard about CLA360"
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!validateForm()) return

        setIsLoading(true)

        localStorage.setItem("additionalInfo_data", JSON.stringify(formData))
        localStorage.setItem("additionalInfo_completed", "true")

        window.dispatchEvent(new Event("formStatusUpdate"))

        await new Promise((resolve) => setTimeout(resolve, 1000))
        setIsLoading(false)

        // Show success message or redirect to completion page
        alert("Application submitted successfully!")
        router.push("/student")
    }

    const handlePrevious = () => {
        localStorage.setItem("additionalInfo_data", JSON.stringify(formData))
        router.push("/student/application-form/health-info")
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            <div className="container mx-auto px-4 py-8">
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
                    <Card className="max-w-4xl mx-auto border-none shadow-xl bg-white/80 backdrop-blur-sm">

                        <CardHeader className="text-center pb-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm"></div>
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                                className="relative mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-6 backdrop-blur-sm"
                            >
                                <FileText className="w-8 h-8 text-white" />
                            </motion.div>
                            <CardTitle className="text-4xl font-bold relative"> Additional Info</CardTitle>
                            <p className="text-purple-100 mt-2 relative"> Please provide additional information about yourself </p>
                        </CardHeader>

                        <CardContent className="space-y-8 pt-2">
                            <form onSubmit={handleSubmit} className="space-y-8">
                                {/* Personal Statement */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                    className="space-y-4"
                                >
                                    <div className="space-y-2">
                                        <Label htmlFor="personalStatement" className="text-sm font-medium text-gray-700">
                                            Personal Statement *
                                        </Label>
                                        <p className="text-xs text-gray-500">500 - 1000 words</p>
                                        <Textarea
                                            id="personalStatement"
                                            value={formData.personalStatement}
                                            onChange={(e) => setFormData((prev) => ({ ...prev, personalStatement: e.target.value }))}
                                            className={cn(
                                                "min-h-[200px] border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                errors.personalStatement && "border-red-500",
                                            )}
                                            placeholder="Write your personal statement here..."
                                        />
                                        <div className="flex justify-between items-center">
                                            <p
                                                className={cn(
                                                    "text-xs",
                                                    wordCount < 500 ? "text-red-500" : wordCount > 1000 ? "text-red-500" : "text-green-600",
                                                )}
                                            >
                                                {wordCount} words
                                            </p>
                                            {errors.personalStatement && <p className="text-sm text-red-600">{errors.personalStatement}</p>}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* References */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                    className="space-y-6"
                                >
                                    <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                                        <Users className="w-6 h-6 text-purple-600" />
                                        <h3 className="text-xl font-semibold text-gray-800">References</h3>
                                    </div>

                                    <div className="grid md:grid-cols-2 gap-8">
                                        {/* Reference 1 */}
                                        <div className="space-y-4">
                                            <h4 className="text-lg font-medium text-purple-600">Reference 1</h4>

                                            <div className="space-y-2">
                                                <Label className="text-sm font-medium text-gray-700">Title *</Label>
                                                <Select
                                                    value={formData.reference1.title}
                                                    onValueChange={(value) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference1: { ...prev.reference1, title: value },
                                                        }))
                                                    }
                                                >
                                                    <SelectTrigger
                                                        className={cn(
                                                            "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                            errors.reference1Title && "border-red-500",
                                                        )}
                                                    >
                                                        <SelectValue placeholder="Choose an option" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="Mr.">Mr.</SelectItem>
                                                        <SelectItem value="Mrs.">Mrs.</SelectItem>
                                                        <SelectItem value="Ms.">Ms.</SelectItem>
                                                        <SelectItem value="Dr.">Dr.</SelectItem>
                                                        <SelectItem value="Prof.">Prof.</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors.reference1Title && <p className="text-sm text-red-600">{errors.reference1Title}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="ref1Name" className="text-sm font-medium text-gray-700">
                                                    Name *
                                                </Label>
                                                <Input
                                                    id="ref1Name"
                                                    value={formData.reference1.name}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference1: { ...prev.reference1, name: e.target.value },
                                                        }))
                                                    }
                                                    className={cn(
                                                        "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.reference1Name && "border-red-500",
                                                    )}
                                                    placeholder="Enter name"
                                                />
                                                {errors.reference1Name && <p className="text-sm text-red-600">{errors.reference1Name}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="ref1Organization" className="text-sm font-medium text-gray-700">
                                                    Organisation *
                                                </Label>
                                                <Input
                                                    id="ref1Organization"
                                                    value={formData.reference1.organization}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference1: { ...prev.reference1, organization: e.target.value },
                                                        }))
                                                    }
                                                    className={cn(
                                                        "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.reference1Organization && "border-red-500",
                                                    )}
                                                    placeholder="Enter organisation"
                                                />
                                                {errors.reference1Organization && (
                                                    <p className="text-sm text-red-600">{errors.reference1Organization}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="ref1Email" className="text-sm font-medium text-gray-700">
                                                    Email *
                                                </Label>
                                                <Input
                                                    id="ref1Email"
                                                    type="email"
                                                    value={formData.reference1.email}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference1: { ...prev.reference1, email: e.target.value },
                                                        }))
                                                    }
                                                    className={cn(
                                                        "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.reference1Email && "border-red-500",
                                                    )}
                                                    placeholder="Enter email"
                                                />
                                                {errors.reference1Email && <p className="text-sm text-red-600">{errors.reference1Email}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="ref1Phone" className="text-sm font-medium text-gray-700">
                                                    Phone *
                                                </Label>
                                                <Input
                                                    id="ref1Phone"
                                                    value={formData.reference1.phone}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference1: { ...prev.reference1, phone: e.target.value },
                                                        }))
                                                    }
                                                    className={cn(
                                                        "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.reference1Phone && "border-red-500",
                                                    )}
                                                    placeholder="Enter phone number"
                                                />
                                                {errors.reference1Phone && <p className="text-sm text-red-600">{errors.reference1Phone}</p>}
                                            </div>
                                        </div>

                                        {/* Reference 2 */}
                                        <div className="space-y-4">
                                            <h4 className="text-lg font-medium text-purple-600">Reference 2</h4>

                                            <div className="space-y-2">
                                                <Label className="text-sm font-medium text-gray-700">Title *</Label>
                                                <Select
                                                    value={formData.reference2.title}
                                                    onValueChange={(value) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference2: { ...prev.reference2, title: value },
                                                        }))
                                                    }
                                                >
                                                    <SelectTrigger
                                                        className={cn(
                                                            "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                            errors.reference2Title && "border-red-500",
                                                        )}
                                                    >
                                                        <SelectValue placeholder="Choose an option" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="Mr.">Mr.</SelectItem>
                                                        <SelectItem value="Mrs.">Mrs.</SelectItem>
                                                        <SelectItem value="Ms.">Ms.</SelectItem>
                                                        <SelectItem value="Dr.">Dr.</SelectItem>
                                                        <SelectItem value="Prof.">Prof.</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors.reference2Title && <p className="text-sm text-red-600">{errors.reference2Title}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="ref2Name" className="text-sm font-medium text-gray-700">
                                                    Name *
                                                </Label>
                                                <Input
                                                    id="ref2Name"
                                                    value={formData.reference2.name}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference2: { ...prev.reference2, name: e.target.value },
                                                        }))
                                                    }
                                                    className={cn(
                                                        "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.reference2Name && "border-red-500",
                                                    )}
                                                    placeholder="Enter name"
                                                />
                                                {errors.reference2Name && <p className="text-sm text-red-600">{errors.reference2Name}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="ref2Organization" className="text-sm font-medium text-gray-700">
                                                    Organisation *
                                                </Label>
                                                <Input
                                                    id="ref2Organization"
                                                    value={formData.reference2.organization}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference2: { ...prev.reference2, organization: e.target.value },
                                                        }))
                                                    }
                                                    className={cn(
                                                        "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.reference2Organization && "border-red-500",
                                                    )}
                                                    placeholder="Enter organisation"
                                                />
                                                {errors.reference2Organization && (
                                                    <p className="text-sm text-red-600">{errors.reference2Organization}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="ref2Email" className="text-sm font-medium text-gray-700">
                                                    Email *
                                                </Label>
                                                <Input
                                                    id="ref2Email"
                                                    type="email"
                                                    value={formData.reference2.email}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference2: { ...prev.reference2, email: e.target.value },
                                                        }))
                                                    }
                                                    className={cn(
                                                        "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.reference2Email && "border-red-500",
                                                    )}
                                                    placeholder="Enter email"
                                                />
                                                {errors.reference2Email && <p className="text-sm text-red-600">{errors.reference2Email}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="ref2Phone" className="text-sm font-medium text-gray-700">
                                                    Phone *
                                                </Label>
                                                <Input
                                                    id="ref2Phone"
                                                    value={formData.reference2.phone}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            reference2: { ...prev.reference2, phone: e.target.value },
                                                        }))
                                                    }
                                                    className={cn(
                                                        "h-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                                                        errors.reference2Phone && "border-red-500",
                                                    )}
                                                    placeholder="Enter phone number"
                                                />
                                                {errors.reference2Phone && <p className="text-sm text-red-600">{errors.reference2Phone}</p>}
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Additional Questions */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.3 }}
                                    className="space-y-6"
                                >
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                id="criminalRecord"
                                                checked={formData.hasCriminalRecord}
                                                onCheckedChange={(checked) =>
                                                    setFormData((prev) => ({ ...prev, hasCriminalRecord: checked as boolean }))
                                                }
                                            />
                                            <Label htmlFor="criminalRecord" className="text-sm font-medium text-gray-700">
                                                Have a criminal record?
                                            </Label>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                id="expelled"
                                                checked={formData.hasBeenExpelled}
                                                onCheckedChange={(checked) =>
                                                    setFormData((prev) => ({ ...prev, hasBeenExpelled: checked as boolean }))
                                                }
                                            />
                                            <Label htmlFor="expelled" className="text-sm font-medium text-gray-700">
                                                Have you been expelled or suspended from any school?
                                            </Label>
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label className="text-sm font-medium text-gray-700">How did you hear about CLA360? *</Label>
                                        <Select
                                            value={formData.howDidYouHear}
                                            onValueChange={(value) => setFormData((prev) => ({ ...prev, howDidYouHear: value }))}
                                        >
                                            <SelectTrigger
                                                className={cn(
                                                    "h-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                                                    errors.howDidYouHear && "border-red-500",
                                                )}
                                            >
                                                <SelectValue placeholder="Choose an option" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="Social Media">Social Media</SelectItem>
                                                <SelectItem value="Search Engine">Search Engine</SelectItem>
                                                <SelectItem value="Friend/Family">Friend/Family</SelectItem>
                                                <SelectItem value="School Counselor">School Counselor</SelectItem>
                                                <SelectItem value="Advertisement">Advertisement</SelectItem>
                                                <SelectItem value="Education Fair">Education Fair</SelectItem>
                                                <SelectItem value="Other">Other</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.howDidYouHear && <p className="text-sm text-red-600">{errors.howDidYouHear}</p>}
                                    </div>
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.4 }}
                                    className="flex gap-4 pt-8"
                                >
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handlePrevious}
                                        className="flex-1 h-12 text-gray-600 border-gray-300 hover:bg-gray-50"
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                                    >
                                        {isLoading ? "Submitting..." : "Submit"}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </div>
    )
}
