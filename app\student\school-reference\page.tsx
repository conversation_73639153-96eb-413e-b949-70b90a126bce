"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
    AlertCircle,
    Building,
    CheckCircle,
    ChevronRight,
    Globe,
    Info,
    Mail,
    MapPin,
    Plus,
    Save,
    Send,
    User,
} from "lucide-react"

// Animation variants
const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
}

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
        },
    },
}

export default function SchoolReferencePage() {
    const [ref, inView] = useInView({
        triggerOnce: true,
        threshold: 0.1,
    })

    const [activeTab, setActiveTab] = useState("preferences")
    const [faithBased, setFaithBased] = useState(false)
    const [urbanSetting, setUrbanSetting] = useState(false)
    const [internationalStudents, setInternationalStudents] = useState(true)

    // Mock data for saved references
    const savedReferences = [
        {
            id: "ref-001",
            name: "Dr. Sarah Johnson",
            position: "Academic Advisor",
            institution: "Lincoln High School",
            email: "<EMAIL>",
            status: "confirmed",
        },
        {
            id: "ref-002",
            name: "Prof. Michael Chen",
            position: "Department Chair",
            institution: "State University",
            email: "<EMAIL>",
            status: "pending",
        },
    ]

    return (
        <div className="container mx-auto p-6">
            {/* Page Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900">School References & Preferences</h1>
                <p className="text-gray-600">Manage your school references and set your university preferences</p>
            </div>

            {/* Main Content */}
            <Tabs defaultValue="preferences" value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="mb-6 grid w-full max-w-md grid-cols-2">
                    <TabsTrigger
                        value="preferences"
                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                    >
                        University Preferences
                    </TabsTrigger>
                    <TabsTrigger
                        value="references"
                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                    >
                        Academic References
                    </TabsTrigger>
                </TabsList>

                {/* Preferences Tab */}
                <TabsContent value="preferences">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Left Column - Preferences Form */}
                        <motion.div
                            ref={ref}
                            initial="hidden"
                            animate={inView ? "visible" : "hidden"}
                            variants={fadeInUp}
                            className="md:col-span-2"
                        >
                            <Card className="border-none shadow-md">
                                <CardHeader>
                                    <CardTitle>University Preferences</CardTitle>
                                    <CardDescription>
                                        Tell us your preferences to help us find the best university matches for you
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        {/* Location Preference */}
                                        <div className="space-y-2">
                                            <Label htmlFor="location">Preferred Location</Label>
                                            <div className="relative">
                                                <MapPin className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                                <Input
                                                    id="location"
                                                    placeholder="Enter country or region"
                                                    className="pl-10"
                                                    defaultValue="United States, Canada, United Kingdom"
                                                />
                                            </div>
                                            <p className="text-xs text-gray-500">You can enter multiple locations separated by commas</p>
                                        </div>

                                        {/* University Size */}
                                        <div className="space-y-2">
                                            <Label htmlFor="size">University Size</Label>
                                            <Select defaultValue="medium">
                                                <SelectTrigger id="size" className="w-full">
                                                    <SelectValue placeholder="Select Preferred School Size" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="small">Small (Less than 5,000 students)</SelectItem>
                                                    <SelectItem value="medium">Medium (5,000 - 15,000 students)</SelectItem>
                                                    <SelectItem value="large">Large (More than 15,000 students)</SelectItem>
                                                    <SelectItem value="any">No Preference</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        {/* Program Type */}
                                        <div className="space-y-2">
                                            <Label htmlFor="program">Program Type</Label>
                                            <Select defaultValue="undergraduate">
                                                <SelectTrigger id="program">
                                                    <SelectValue placeholder="Select Program Type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="undergraduate">Undergraduate</SelectItem>
                                                    <SelectItem value="graduate">Graduate</SelectItem>
                                                    <SelectItem value="phd">PhD / Doctoral</SelectItem>
                                                    <SelectItem value="certificate">Certificate Program</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        {/* Field of Study */}
                                        <div className="space-y-2">
                                            <Label htmlFor="field">Field of Study</Label>
                                            <Select defaultValue="computer-science">
                                                <SelectTrigger id="field">
                                                    <SelectValue placeholder="Select Field of Study" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="business">Business & Management</SelectItem>
                                                    <SelectItem value="computer-science">Computer Science & IT</SelectItem>
                                                    <SelectItem value="engineering">Engineering</SelectItem>
                                                    <SelectItem value="medicine">Medicine & Health Sciences</SelectItem>
                                                    <SelectItem value="arts">Arts & Humanities</SelectItem>
                                                    <SelectItem value="social-sciences">Social Sciences</SelectItem>
                                                    <SelectItem value="natural-sciences">Natural Sciences</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        {/* Budget Range */}
                                        <div className="space-y-2">
                                            <Label htmlFor="budget">Annual Budget Range (USD)</Label>
                                            <Select defaultValue="20000-40000">
                                                <SelectTrigger id="budget">
                                                    <SelectValue placeholder="Select Budget Range" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="under-20000">Under $20,000</SelectItem>
                                                    <SelectItem value="20000-40000">$20,000 - $40,000</SelectItem>
                                                    <SelectItem value="40000-60000">$40,000 - $60,000</SelectItem>
                                                    <SelectItem value="above-60000">Above $60,000</SelectItem>
                                                    <SelectItem value="scholarship-only">Scholarship Only</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        {/* Ranking Preference */}
                                        <div className="space-y-2">
                                            <Label htmlFor="ranking">University Ranking</Label>
                                            <Select defaultValue="top-100">
                                                <SelectTrigger id="ranking">
                                                    <SelectValue placeholder="Select Ranking Preference" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="top-10">Top 10 Globally</SelectItem>
                                                    <SelectItem value="top-50">Top 50 Globally</SelectItem>
                                                    <SelectItem value="top-100">Top 100 Globally</SelectItem>
                                                    <SelectItem value="top-200">Top 200 Globally</SelectItem>
                                                    <SelectItem value="any">Any Ranking</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>

                                    {/* Additional Preferences */}
                                    <div className="space-y-4 rounded-lg border border-gray-100 bg-gray-50 p-4">
                                        <h3 className="font-medium text-gray-900">Additional Preferences</h3>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="flex items-center justify-between space-x-2 rounded-lg border border-gray-200 bg-white p-3">
                                                <div className="flex items-center space-x-2">
                                                    <Building className="h-4 w-4 text-gray-500" />
                                                    <Label htmlFor="faith-based" className="text-sm font-normal">
                                                        Faith-based Institution
                                                    </Label>
                                                </div>
                                                <Switch id="faith-based" checked={faithBased} onCheckedChange={setFaithBased} />
                                            </div>

                                            <div className="flex items-center justify-between space-x-2 rounded-lg border border-gray-200 bg-white p-3">
                                                <div className="flex items-center space-x-2">
                                                    <MapPin className="h-4 w-4 text-gray-500" />
                                                    <Label htmlFor="urban-setting" className="text-sm font-normal">
                                                        Urban Setting
                                                    </Label>
                                                </div>
                                                <Switch id="urban-setting" checked={urbanSetting} onCheckedChange={setUrbanSetting} />
                                            </div>

                                            <div className="flex items-center justify-between space-x-2 rounded-lg border border-gray-200 bg-white p-3">
                                                <div className="flex items-center space-x-2">
                                                    <Globe className="h-4 w-4 text-gray-500" />
                                                    <Label htmlFor="international-students" className="text-sm font-normal">
                                                        Strong International Community
                                                    </Label>
                                                </div>
                                                <Switch
                                                    id="international-students"
                                                    checked={internationalStudents}
                                                    onCheckedChange={setInternationalStudents}
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Additional Notes */}
                                    <div className="space-y-2">
                                        <Label htmlFor="notes">Additional Notes</Label>
                                        <Textarea
                                            id="notes"
                                            placeholder="Any other preferences or requirements..."
                                            className="min-h-[100px]"
                                        />
                                    </div>
                                </CardContent>
                                <CardFooter className="flex justify-between border-t border-gray-100 bg-gray-50 px-6 py-4">
                                    <Button variant="outline">Reset</Button>
                                    <Button className="bg-purple-600 hover:bg-purple-700">
                                        <Save className="mr-2 h-4 w-4" />
                                        Save Preferences
                                    </Button>
                                </CardFooter>
                            </Card>
                        </motion.div>

                        {/* Right Column - Recommendations */}
                        <motion.div
                            initial="hidden"
                            animate={inView ? "visible" : "hidden"}
                            variants={fadeInUp}
                            className="md:col-span-1"
                        >
                            <Card className="border-none shadow-md">
                                <CardHeader className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                                    <CardTitle className="text-lg">Based on Your Preferences</CardTitle>
                                    <CardDescription className="text-blue-100">Universities that match your criteria</CardDescription>
                                </CardHeader>
                                <CardContent className="p-0">
                                    <div className="divide-y divide-gray-100">
                                        {[
                                            {
                                                name: "University of California, Berkeley",
                                                location: "United States",
                                                match: 95,
                                                programs: ["Computer Science", "Data Science"],
                                            },
                                            {
                                                name: "University of Toronto",
                                                location: "Canada",
                                                match: 92,
                                                programs: ["Computer Science", "AI & Machine Learning"],
                                            },
                                            {
                                                name: "Imperial College London",
                                                location: "United Kingdom",
                                                match: 88,
                                                programs: ["Computing", "Software Engineering"],
                                            },
                                        ].map((university, index) => (
                                            <div key={index} className="p-4 hover:bg-gray-50">
                                                <div className="mb-2 flex items-center justify-between">
                                                    <h3 className="font-medium text-gray-900">{university.name}</h3>
                                                    <Badge className="bg-green-100 text-green-800">{university.match}% Match</Badge>
                                                </div>
                                                <div className="flex items-center gap-1 text-xs text-gray-500">
                                                    <MapPin className="h-3 w-3" />
                                                    <span>{university.location}</span>
                                                </div>
                                                <div className="mt-2 flex flex-wrap gap-1">
                                                    {university.programs.map((program, i) => (
                                                        <Badge key={i} variant="outline" className="font-normal">
                                                            {program}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                                <CardFooter className="border-t border-gray-100 p-4">
                                    <Button variant="ghost" className="w-full justify-between text-purple-600 hover:text-purple-700">
                                        <span>View All Matches</span>
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </CardFooter>
                            </Card>

                            <div className="mt-6">
                                <Card className="border-none shadow-md">
                                    <CardHeader className="pb-2">
                                        <CardTitle className="text-lg">Need Help?</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="rounded-lg bg-blue-50 p-4 text-blue-800">
                                            <div className="flex items-start gap-3">
                                                <Info className="mt-0.5 h-5 w-5 flex-shrink-0 text-blue-600" />
                                                <div>
                                                    <p className="text-sm">
                                                        Our education counselors can help you refine your preferences and find the perfect
                                                        university match.
                                                    </p>
                                                    <Button variant="link" className="mt-2 h-auto p-0 text-sm text-blue-700 hover:text-blue-800">
                                                        Schedule a consultation
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </motion.div>
                    </div>
                </TabsContent>

                {/* References Tab */}
                <TabsContent value="references">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Left Column - Add Reference Form */}
                        <motion.div
                            initial="hidden"
                            animate={inView ? "visible" : "hidden"}
                            variants={fadeInUp}
                            className="md:col-span-1"
                        >
                            <Card className="border-none shadow-md">
                                <CardHeader>
                                    <CardTitle>Add Academic Reference</CardTitle>
                                    <CardDescription>Add details of your academic references</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="ref-name">Reference Name</Label>
                                        <div className="relative">
                                            <User className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                            <Input id="ref-name" placeholder="Full name" className="pl-10" />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="ref-position">Position/Title</Label>
                                        <Input id="ref-position" placeholder="e.g. Professor, Academic Advisor" />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="ref-institution">Institution</Label>
                                        <div className="relative">
                                            <Building className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                            <Input id="ref-institution" placeholder="School or university name" className="pl-10" />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="ref-email">Email Address</Label>
                                        <div className="relative">
                                            <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                            <Input id="ref-email" type="email" placeholder="Academic email address" className="pl-10" />
                                        </div>
                                        <p className="text-xs text-gray-500">We recommend using their institutional email address</p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="ref-relationship">Relationship</Label>
                                        <Select>
                                            <SelectTrigger id="ref-relationship">
                                                <SelectValue placeholder="Select your relationship" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="professor">Professor</SelectItem>
                                                <SelectItem value="advisor">Academic Advisor</SelectItem>
                                                <SelectItem value="supervisor">Research Supervisor</SelectItem>
                                                <SelectItem value="employer">Employer</SelectItem>
                                                <SelectItem value="other">Other</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </CardContent>
                                <CardFooter className="border-t border-gray-100 bg-gray-50 px-6 py-4">
                                    <Button className="w-full bg-purple-600 hover:bg-purple-700">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Add Reference
                                    </Button>
                                </CardFooter>
                            </Card>
                        </motion.div>

                        {/* Right Column - Saved References */}
                        <motion.div
                            initial="hidden"
                            animate={inView ? "visible" : "hidden"}
                            variants={fadeInUp}
                            className="md:col-span-2"
                        >
                            <Card className="border-none shadow-md">
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <CardTitle>Your Academic References</CardTitle>
                                        <Badge variant="outline" className="font-normal">
                                            {savedReferences.length} References
                                        </Badge>
                                    </div>
                                    <CardDescription>Manage your academic references for your applications</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {savedReferences.map((reference) => (
                                            <div
                                                key={reference.id}
                                                className="rounded-lg border border-gray-100 p-4 transition-all hover:bg-gray-50"
                                            >
                                                <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                                                    <div>
                                                        <div className="flex items-center gap-2">
                                                            <h3 className="font-medium text-gray-900">{reference.name}</h3>
                                                            <Badge
                                                                className={
                                                                    reference.status === "confirmed"
                                                                        ? "bg-green-100 text-green-800"
                                                                        : "bg-amber-100 text-amber-800"
                                                                }
                                                            >
                                                                {reference.status === "confirmed" ? "Confirmed" : "Pending"}
                                                            </Badge>
                                                        </div>
                                                        <p className="text-sm text-gray-600">
                                                            {reference.position} at {reference.institution}
                                                        </p>
                                                        <p className="text-xs text-gray-500">{reference.email}</p>
                                                    </div>

                                                    <div className="ml-auto flex flex-wrap gap-2">
                                                        {reference.status === "pending" ? (
                                                            <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                                                                <Send className="mr-1.5 h-3.5 w-3.5" />
                                                                Send Reminder
                                                            </Button>
                                                        ) : (
                                                            <Button variant="outline" size="sm">
                                                                <CheckCircle className="mr-1.5 h-3.5 w-3.5 text-green-500" />
                                                                Confirmed
                                                            </Button>
                                                        )}
                                                        <Button variant="outline" size="sm">
                                                            Edit
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>

                                    {savedReferences.length === 0 && (
                                        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-200 bg-gray-50 p-8 text-center">
                                            <div className="mb-3 rounded-full bg-gray-100 p-3">
                                                <User className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <h3 className="mb-1 font-medium text-gray-900">No references added yet</h3>
                                            <p className="mb-4 text-sm text-gray-500">
                                                Add your academic references to strengthen your application
                                            </p>
                                            <Button className="bg-purple-600 hover:bg-purple-700">
                                                <Plus className="mr-2 h-4 w-4" />
                                                Add Your First Reference
                                            </Button>
                                        </div>
                                    )}
                                </CardContent>

                                <div className="border-t border-gray-100 px-6 py-4">
                                    <div className="rounded-lg bg-amber-50 p-4">
                                        <div className="flex items-start gap-3">
                                            <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-amber-600" />
                                            <div>
                                                <h4 className="font-medium text-amber-800">Reference Requirements</h4>
                                                <p className="mt-1 text-sm text-amber-700">
                                                    Most universities require 2-3 academic references. Make sure your references are from
                                                    professors or academic advisors who know your work well.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Card>

                            {/* Reference Guide */}
                            <motion.div initial="hidden" animate={inView ? "visible" : "hidden"} variants={fadeInUp} className="mt-6">
                                <Card className="border-none shadow-md">
                                    <CardHeader className="pb-2">
                                        <CardTitle className="text-lg">Reference Guide</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            <div className="flex items-start gap-3">
                                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-purple-100 text-purple-600">
                                                    <span className="text-xs font-bold">1</span>
                                                </div>
                                                <div>
                                                    <h4 className="font-medium text-gray-900">Choose the Right References</h4>
                                                    <p className="text-sm text-gray-600">
                                                        Select professors or advisors who know your academic abilities well and can speak to your
                                                        strengths.
                                                    </p>
                                                </div>
                                            </div>

                                            <div className="flex items-start gap-3">
                                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-purple-100 text-purple-600">
                                                    <span className="text-xs font-bold">2</span>
                                                </div>
                                                <div>
                                                    <h4 className="font-medium text-gray-900">Ask Permission First</h4>
                                                    <p className="text-sm text-gray-600">
                                                        Always ask your references for permission before adding them to your application.
                                                    </p>
                                                </div>
                                            </div>

                                            <div className="flex items-start gap-3">
                                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-purple-100 text-purple-600">
                                                    <span className="text-xs font-bold">3</span>
                                                </div>
                                                <div>
                                                    <h4 className="font-medium text-gray-900">Provide Context</h4>
                                                    <p className="text-sm text-gray-600">
                                                        Share your application goals with your references to help them write more targeted letters.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>
                        </motion.div>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    )
}
