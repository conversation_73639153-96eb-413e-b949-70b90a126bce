"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { AlertCircle, CheckCircle, Clock, Download, Eye, FileText, HelpCircle, Plus, Upload, X } from "lucide-react"

// Animation variants
const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
}

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
        },
    },
}

export default function TranscriptPage() {
    const [ref, inView] = useInView({
        triggerOnce: true,
        threshold: 0.1,
    })

    const [activeTab, setActiveTab] = useState("uploaded")

    // Mock data for transcripts
    const transcripts = [
        {
            id: "tr-001",
            name: "High School Transcript",
            institution: "Lincoln High School",
            uploadDate: "May 10, 2025",
            status: "verified",
            fileSize: "2.4 MB",
            fileType: "PDF",
        },
        {
            id: "tr-002",
            name: "Bachelor's Degree Transcript",
            institution: "State University",
            uploadDate: "May 12, 2025",
            status: "pending",
            fileSize: "3.1 MB",
            fileType: "PDF",
        },
        {
            id: "tr-003",
            name: "Language Proficiency Certificate",
            institution: "International Language Center",
            uploadDate: "May 15, 2025",
            status: "rejected",
            fileSize: "1.8 MB",
            fileType: "PDF",
            rejectionReason: "Document is not clearly legible. Please upload a higher quality scan.",
        },
    ]

    // Required documents
    const requiredDocuments = [
        {
            id: "doc-001",
            name: "Master's Degree Transcript",
            description: "Official transcript from your Master's program",
            required: true,
        },
        {
            id: "doc-002",
            name: "Standardized Test Scores",
            description: "GRE, GMAT, or equivalent test scores",
            required: true,
        },
        {
            id: "doc-003",
            name: "Professional Certifications",
            description: "Any relevant professional certifications",
            required: false,
        },
    ]

    // Get status icon based on status
    const getStatusIcon = (status: string) => {
        switch (status) {
            case "verified":
                return <CheckCircle className="h-5 w-5 text-green-500" />
            case "pending":
                return <Clock className="h-5 w-5 text-amber-500" />
            case "rejected":
                return <X className="h-5 w-5 text-red-500" />
            default:
                return <HelpCircle className="h-5 w-5 text-gray-500" />
        }
    }

    // Get status text based on status
    const getStatusText = (status: string) => {
        switch (status) {
            case "verified":
                return "Verified"
            case "pending":
                return "Pending Verification"
            case "rejected":
                return "Rejected"
            default:
                return "Unknown"
        }
    }

    // Get status color based on status
    const getStatusColor = (status: string) => {
        switch (status) {
            case "verified":
                return "bg-green-100 text-green-800"
            case "pending":
                return "bg-amber-100 text-amber-800"
            case "rejected":
                return "bg-red-100 text-red-800"
            default:
                return "bg-gray-100 text-gray-800"
        }
    }

    return (
        <div className="container mx-auto p-6">
            {/* Page Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900">Academic Transcripts</h1>
                <p className="text-gray-600">Upload and manage your academic transcripts and certificates</p>
            </div>

            {/* Main Content */}
            <div className="grid gap-6 md:grid-cols-3">
                {/* Left Column - Upload and Instructions */}
                <motion.div
                    ref={ref}
                    initial="hidden"
                    animate={inView ? "visible" : "hidden"}
                    variants={fadeInUp}
                    className="md:col-span-1"
                >
                    <Card className="border-none shadow-md">
                        <CardHeader className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                            <CardTitle>Upload Transcript</CardTitle>
                            <CardDescription className="text-blue-100">
                                Upload your official academic transcripts here
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-6">
                            <div className="mb-6 rounded-lg border-2 border-dashed border-gray-200 bg-gray-50 p-6 text-center">
                                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
                                    <Upload className="h-8 w-8 text-purple-600" />
                                </div>
                                <h3 className="mb-2 text-sm font-medium text-gray-900">Drag and drop your files here</h3>
                                <p className="mb-4 text-xs text-gray-500">Supported formats: PDF, JPG, PNG (Max 10MB)</p>
                                <Button className="bg-purple-600 hover:bg-purple-700">
                                    <Upload className="mr-2 h-4 w-4" />
                                    Select Files
                                </Button>
                            </div>

                            <div className="space-y-4">
                                <h3 className="font-medium text-gray-900">Guidelines:</h3>
                                <ul className="space-y-2 text-sm text-gray-600">
                                    <li className="flex items-start gap-2">
                                        <CheckCircle className="mt-0.5 h-4 w-4 text-green-500" />
                                        <span>Upload official transcripts issued by your institution</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <CheckCircle className="mt-0.5 h-4 w-4 text-green-500" />
                                        <span>Ensure all pages are included and clearly legible</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <CheckCircle className="mt-0.5 h-4 w-4 text-green-500" />
                                        <span>Documents in languages other than English must include certified translations</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <AlertCircle className="mt-0.5 h-4 w-4 text-amber-500" />
                                        <span>Verification typically takes 2-3 business days</span>
                                    </li>
                                </ul>
                            </div>
                        </CardContent>
                        <CardFooter className="bg-gray-50 px-6 py-4">
                            <Button variant="outline" className="w-full">
                                <HelpCircle className="mr-2 h-4 w-4" />
                                Need Help?
                            </Button>
                        </CardFooter>
                    </Card>
                </motion.div>

                {/* Right Column - Transcript Management */}
                <motion.div
                    initial="hidden"
                    animate={inView ? "visible" : "hidden"}
                    variants={fadeInUp}
                    className="md:col-span-2"
                >
                    <Card className="border-none shadow-md">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle>Your Transcripts</CardTitle>
                                <Badge variant="outline" className="font-normal">
                                    {transcripts.length} Documents
                                </Badge>
                            </div>
                            <CardDescription>Manage your uploaded academic documents</CardDescription>
                        </CardHeader>

                        <Tabs defaultValue="uploaded" value={activeTab} onValueChange={setActiveTab} className="w-full">
                            <div className="px-6">
                                <TabsList className="grid w-full grid-cols-2">
                                    <TabsTrigger
                                        value="uploaded"
                                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                                    >
                                        Uploaded Documents
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="required"
                                        className="data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                                    >
                                        Required Documents
                                    </TabsTrigger>
                                </TabsList>
                            </div>

                            <TabsContent value="uploaded" className="px-0 pt-4">
                                <div className="space-y-4 px-6">
                                    {transcripts.map((transcript) => (
                                        <div
                                            key={transcript.id}
                                            className="rounded-lg border border-gray-100 p-4 transition-all hover:bg-gray-50"
                                        >
                                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 text-purple-600">
                                                        <FileText className="h-5 w-5" />
                                                    </div>
                                                    <div>
                                                        <h4 className="font-medium text-gray-900">{transcript.name}</h4>
                                                        <p className="text-xs text-gray-500">{transcript.institution}</p>
                                                    </div>
                                                </div>
                                                <div className="ml-auto flex flex-col items-start gap-1 sm:items-end">
                                                    <Badge className={getStatusColor(transcript.status)}>
                                                        <span className="mr-1.5 flex items-center">{getStatusIcon(transcript.status)}</span>
                                                        {getStatusText(transcript.status)}
                                                    </Badge>
                                                    <p className="text-xs text-gray-500">Uploaded on {transcript.uploadDate}</p>
                                                </div>
                                            </div>

                                            {transcript.status === "rejected" && (
                                                <div className="mt-3 rounded-md bg-red-50 p-3 text-sm text-red-800">
                                                    <div className="flex items-start gap-2">
                                                        <AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0" />
                                                        <span>{transcript.rejectionReason}</span>
                                                    </div>
                                                </div>
                                            )}

                                            <div className="mt-4 flex flex-wrap gap-2">
                                                <Button variant="outline" size="sm" className="h-8">
                                                    <Eye className="mr-1.5 h-3.5 w-3.5" />
                                                    View
                                                </Button>
                                                <Button variant="outline" size="sm" className="h-8">
                                                    <Download className="mr-1.5 h-3.5 w-3.5" />
                                                    Download
                                                </Button>
                                                {transcript.status === "rejected" && (
                                                    <Button size="sm" className="h-8 bg-purple-600 hover:bg-purple-700">
                                                        <Upload className="mr-1.5 h-3.5 w-3.5" />
                                                        Re-upload
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <div className="mt-4 border-t border-gray-100 px-6 py-4">
                                    <Button variant="outline" className="w-full">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Upload New Document
                                    </Button>
                                </div>
                            </TabsContent>

                            <TabsContent value="required" className="px-0 pt-4">
                                <div className="space-y-4 px-6">
                                    {requiredDocuments.map((document) => (
                                        <div
                                            key={document.id}
                                            className="rounded-lg border border-gray-100 p-4 transition-all hover:bg-gray-50"
                                        >
                                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 text-purple-600">
                                                        <FileText className="h-5 w-5" />
                                                    </div>
                                                    <div>
                                                        <div className="flex items-center gap-2">
                                                            <h4 className="font-medium text-gray-900">{document.name}</h4>
                                                            {document.required && (
                                                                <Badge variant="outline" className="border-red-200 bg-red-50 text-red-700">
                                                                    Required
                                                                </Badge>
                                                            )}
                                                        </div>
                                                        <p className="text-xs text-gray-500">{document.description}</p>
                                                    </div>
                                                </div>
                                                <div className="ml-auto">
                                                    <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                                                        <Upload className="mr-1.5 h-3.5 w-3.5" />
                                                        Upload
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <div className="mt-6 bg-blue-50 px-6 py-4">
                                    <div className="flex items-start gap-3">
                                        <div className="rounded-full bg-blue-100 p-2 text-blue-600">
                                            <AlertCircle className="h-5 w-5" />
                                        </div>
                                        <div>
                                            <h4 className="font-medium text-blue-900">Document Verification</h4>
                                            <p className="mt-1 text-sm text-blue-700">
                                                All required documents must be uploaded and verified before your application can be processed.
                                                Verification typically takes 2-3 business days.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                        </Tabs>
                    </Card>

                    {/* Verification Progress */}
                    <motion.div initial="hidden" animate={inView ? "visible" : "hidden"} variants={fadeInUp} className="mt-6">
                        <Card className="border-none shadow-md">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-lg">Verification Progress</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="mb-2 flex items-center justify-between">
                                    <span className="text-sm font-medium">Overall Completion</span>
                                    <span className="text-sm text-gray-500">2 of 5 documents verified</span>
                                </div>
                                <Progress value={40} className="h-2" />

                                <div className="mt-6 grid grid-cols-2 gap-4 sm:grid-cols-5">
                                    <div className="rounded-lg border border-gray-100 p-3 text-center">
                                        <div className="mx-auto mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                        </div>
                                        <p className="text-xs text-gray-500">High School</p>
                                        <p className="text-sm font-medium text-green-600">Verified</p>
                                    </div>
                                    <div className="rounded-lg border border-gray-100 p-3 text-center">
                                        <div className="mx-auto mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-amber-100">
                                            <Clock className="h-4 w-4 text-amber-600" />
                                        </div>
                                        <p className="text-xs text-gray-500">Bachelor's</p>
                                        <p className="text-sm font-medium text-amber-600">Pending</p>
                                    </div>
                                    <div className="rounded-lg border border-gray-100 p-3 text-center">
                                        <div className="mx-auto mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                                            <X className="h-4 w-4 text-red-600" />
                                        </div>
                                        <p className="text-xs text-gray-500">Language</p>
                                        <p className="text-sm font-medium text-red-600">Rejected</p>
                                    </div>
                                    <div className="rounded-lg border border-gray-100 p-3 text-center">
                                        <div className="mx-auto mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                                            <Upload className="h-4 w-4 text-gray-400" />
                                        </div>
                                        <p className="text-xs text-gray-500">Master's</p>
                                        <p className="text-sm font-medium text-gray-400">Not Uploaded</p>
                                    </div>
                                    <div className="rounded-lg border border-gray-100 p-3 text-center">
                                        <div className="mx-auto mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                                            <Upload className="h-4 w-4 text-gray-400" />
                                        </div>
                                        <p className="text-xs text-gray-500">Test Scores</p>
                                        <p className="text-sm font-medium text-gray-400">Not Uploaded</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>
                </motion.div>
            </div>
        </div>
    )
}
