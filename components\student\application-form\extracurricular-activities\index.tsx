"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { CalendarIcon, Trophy, X, Plus, Star, Award, Heart, Sparkles, GraduationCap, CheckCircle2 } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface Activity {
    id: string
    name: string
}

interface AwardType {
    id: string
    name: string
    reason: string
    date: Date | undefined
}

interface VolunteerWork {
    role: string
    responsibility: string
    startDate: Date | undefined
    endDate: Date | undefined
}

interface FormData {
    highSchoolActivities: Activity[]
    specialTalents: Activity[]
    volunteerWork: VolunteerWork
    awards: AwardType[]
    hasStudiedAbroad: string
}

export default function ExtracurricularActivitiesPage() {
    const router = useRouter()
    const [formData, setFormData] = useState<FormData>({
        highSchoolActivities: [],
        specialTalents: [],
        volunteerWork: {
            role: "",
            responsibility: "",
            startDate: undefined,
            endDate: undefined,
        },
        awards: [],
        hasStudiedAbroad: "",
    })
    const [newActivity, setNewActivity] = useState("")
    const [newTalent, setNewTalent] = useState("")
    const [newAward, setNewAward] = useState({ name: "", reason: "", date: undefined as Date | undefined })
    const [isLoading, setIsLoading] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)

    useEffect(() => {
        const saved = localStorage.getItem("extracurricularActivities_data")
        if (saved) {
            const parsedData = JSON.parse(saved)
            setFormData({
                ...parsedData,
                volunteerWork: {
                    ...parsedData.volunteerWork,
                    startDate: parsedData.volunteerWork.startDate ? new Date(parsedData.volunteerWork.startDate) : undefined,
                    endDate: parsedData.volunteerWork.endDate ? new Date(parsedData.volunteerWork.endDate) : undefined,
                },
                awards: parsedData.awards.map((award: any) => ({
                    ...award,
                    date: award.date ? new Date(award.date) : undefined,
                })),
            })
        }
    }, [])

    const addActivity = () => {
        if (newActivity.trim()) {
            setFormData((prev) => ({
                ...prev,
                highSchoolActivities: [...prev.highSchoolActivities, { id: Date.now().toString(), name: newActivity.trim() }],
            }))
            setNewActivity("")
        }
    }

    const removeActivity = (id: string) => {
        setFormData((prev) => ({
            ...prev,
            highSchoolActivities: prev.highSchoolActivities.filter((activity) => activity.id !== id),
        }))
    }

    const addTalent = () => {
        if (newTalent.trim()) {
            setFormData((prev) => ({
                ...prev,
                specialTalents: [...prev.specialTalents, { id: Date.now().toString(), name: newTalent.trim() }],
            }))
            setNewTalent("")
        }
    }

    const removeTalent = (id: string) => {
        setFormData((prev) => ({
            ...prev,
            specialTalents: prev.specialTalents.filter((talent) => talent.id !== id),
        }))
    }

    const addAward = () => {
        if (newAward.name.trim() && newAward.reason.trim() && newAward.date) {
            setFormData((prev) => ({
                ...prev,
                awards: [...prev.awards, { ...newAward, id: Date.now().toString() }],
            }))
            setNewAward({ name: "", reason: "", date: undefined })
        }
    }

    const removeAward = (id: string) => {
        setFormData((prev) => ({
            ...prev,
            awards: prev.awards.filter((award) => award.id !== id),
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)

        // Save form data
        localStorage.setItem("extracurricularActivities_data", JSON.stringify(formData))
        localStorage.setItem("extracurricularActivities_completed", "true")

        // Trigger sidebar update immediately
        window.dispatchEvent(new Event("storage"))

        // Show success animation
        setShowSuccess(true)

        await new Promise((resolve) => setTimeout(resolve, 1500))
        setIsLoading(false)

        router.push("/student/application-form/work-experience")
    }

    const handlePrevious = () => {
        localStorage.setItem("extracurricularActivities_data", JSON.stringify(formData))
        router.push("/student/application-form/financial-info")
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
            <div className="container mx-auto px-4 py-8">
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
                    <Card className="max-w-5xl mx-auto border-none shadow-2xl bg-white/90 backdrop-blur-sm overflow-hidden">
                        <CardHeader className="text-center pb-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm"></div>
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                                className="relative mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-6 backdrop-blur-sm"
                            >
                                <Trophy className="w-10 h-10 text-white" />
                            </motion.div>
                            <CardTitle className="text-4xl font-bold relative">Extracurricular Activities</CardTitle>
                            <p className="text-purple-100 mt-2 relative">Showcase your achievements and experiences</p>
                        </CardHeader>

                        <CardContent className="p-8 space-y-10">
                            <form onSubmit={handleSubmit} className="space-y-10">
                                {/* High School Activities */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                    className="space-y-6"
                                >
                                    <Card className="border-2 border-purple-100 hover:border-purple-200 transition-colors">
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center gap-3">
                                                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                                    <GraduationCap className="w-5 h-5 text-white" />
                                                </div>
                                                <div>
                                                    <h3 className="text-xl font-semibold text-gray-800">High School Activities</h3>
                                                    <p className="text-sm text-gray-500">Clubs, Societies, Leadership Roles</p>
                                                </div>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="flex gap-3">
                                                <Input
                                                    value={newActivity}
                                                    onChange={(e) => setNewActivity(e.target.value)}
                                                    placeholder="e.g. Robotics Club, Student Council, Drama Society..."
                                                    className="flex-1 h-12 border-2 border-gray-200 focus:border-purple-400 transition-colors"
                                                    onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addActivity())}
                                                />
                                                <Button
                                                    type="button"
                                                    onClick={addActivity}
                                                    className="h-12 px-6 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                                                >
                                                    <Plus className="w-4 h-4 mr-2" />
                                                    Add
                                                </Button>
                                            </div>

                                            <AnimatePresence>
                                                {formData.highSchoolActivities.map((activity, index) => (
                                                    <motion.div
                                                        key={activity.id}
                                                        initial={{ opacity: 0, y: -10 }}
                                                        animate={{ opacity: 1, y: 0 }}
                                                        exit={{ opacity: 0, y: -10 }}
                                                        transition={{ delay: index * 0.1 }}
                                                        className="flex items-center justify-between bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-100"
                                                    >
                                                        <div className="flex items-center gap-3">
                                                            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                                                <Star className="w-4 h-4 text-white" />
                                                            </div>
                                                            <span className="font-medium text-gray-800">{activity.name}</span>
                                                        </div>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeActivity(activity.id)}
                                                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                                        >
                                                            <X className="h-4 w-4" />
                                                        </Button>
                                                    </motion.div>
                                                ))}
                                            </AnimatePresence>
                                        </CardContent>
                                    </Card>
                                </motion.div>

                                {/* Special Talents */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                    className="space-y-6"
                                >
                                    <Card className="border-2 border-blue-100 hover:border-blue-200 transition-colors">
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center gap-3">
                                                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                                                    <Sparkles className="w-5 h-5 text-white" />
                                                </div>
                                                <div>
                                                    <h3 className="text-xl font-semibold text-gray-800">Special Talents or Skills</h3>
                                                    <p className="text-sm text-gray-500">Music, Art, Language, Sports, etc.</p>
                                                </div>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="flex gap-3">
                                                <Input
                                                    value={newTalent}
                                                    onChange={(e) => setNewTalent(e.target.value)}
                                                    placeholder="e.g. Piano, Painting, Spanish, Basketball..."
                                                    className="flex-1 h-12 border-2 border-gray-200 focus:border-blue-400 transition-colors"
                                                    onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTalent())}
                                                />
                                                <Button
                                                    type="button"
                                                    onClick={addTalent}
                                                    className="h-12 px-6 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
                                                >
                                                    <Plus className="w-4 h-4 mr-2" />
                                                    Add
                                                </Button>
                                            </div>

                                            <AnimatePresence>
                                                {formData.specialTalents.map((talent, index) => (
                                                    <motion.div
                                                        key={talent.id}
                                                        initial={{ opacity: 0, y: -10 }}
                                                        animate={{ opacity: 1, y: 0 }}
                                                        exit={{ opacity: 0, y: -10 }}
                                                        transition={{ delay: index * 0.1 }}
                                                        className="flex items-center justify-between bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-xl border border-blue-100"
                                                    >
                                                        <div className="flex items-center gap-3">
                                                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                                                <Sparkles className="w-4 h-4 text-white" />
                                                            </div>
                                                            <span className="font-medium text-gray-800">{talent.name}</span>
                                                        </div>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeTalent(talent.id)}
                                                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                                        >
                                                            <X className="h-4 w-4" />
                                                        </Button>
                                                    </motion.div>
                                                ))}
                                            </AnimatePresence>
                                        </CardContent>
                                    </Card>
                                </motion.div>

                                {/* Volunteer Work */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.3 }}
                                    className="space-y-6"
                                >
                                    <Card className="border-2 border-green-100 hover:border-green-200 transition-colors">
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center gap-3">
                                                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                                                    <Heart className="w-5 h-5 text-white" />
                                                </div>
                                                <div>
                                                    <h3 className="text-xl font-semibold text-gray-800">Volunteer Work / Community Service</h3>
                                                    <p className="text-sm text-gray-500">Community involvement and service activities</p>
                                                </div>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-6">
                                            <div className="grid md:grid-cols-2 gap-6">
                                                <div className="space-y-2">
                                                    <Label htmlFor="volunteerRole" className="text-sm font-semibold text-gray-700">
                                                        Role
                                                    </Label>
                                                    <Input
                                                        id="volunteerRole"
                                                        value={formData.volunteerWork.role}
                                                        onChange={(e) =>
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                volunteerWork: { ...prev.volunteerWork, role: e.target.value },
                                                            }))
                                                        }
                                                        placeholder="e.g. Blood Drive Volunteer"
                                                        className="h-12 border-2 border-gray-200 focus:border-green-400 transition-colors"
                                                    />
                                                </div>

                                                <div className="space-y-2 md:col-span-2">
                                                    <Label htmlFor="volunteerResponsibility" className="text-sm font-semibold text-gray-700">
                                                        Main Responsibility
                                                    </Label>
                                                    <Textarea
                                                        id="volunteerResponsibility"
                                                        value={formData.volunteerWork.responsibility}
                                                        onChange={(e) =>
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                volunteerWork: { ...prev.volunteerWork, responsibility: e.target.value },
                                                            }))
                                                        }
                                                        placeholder="e.g. Help set up donation stations and provide support to donors."
                                                        className="min-h-[100px] border-2 border-gray-200 focus:border-green-400 transition-colors resize-none"
                                                    />
                                                </div>

                                                <div className="space-y-2 md:col-span-2">
                                                    <Label className="text-sm font-semibold text-gray-700">Service Period</Label>
                                                    <div className="flex items-center gap-4">
                                                        <Popover>
                                                            <PopoverTrigger asChild>
                                                                <Button
                                                                    variant="outline"
                                                                    className={cn(
                                                                        "flex-1 justify-start text-left font-normal h-12 border-2 border-gray-200 hover:border-green-400",
                                                                        !formData.volunteerWork.startDate && "text-muted-foreground",
                                                                    )}
                                                                >
                                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                                    {formData.volunteerWork.startDate
                                                                        ? format(formData.volunteerWork.startDate, "PPP")
                                                                        : "Start Date"}
                                                                </Button>
                                                            </PopoverTrigger>
                                                            <PopoverContent className="w-auto p-0" align="start">
                                                                <Calendar
                                                                    mode="single"
                                                                    selected={formData.volunteerWork.startDate}
                                                                    onSelect={(date) =>
                                                                        setFormData((prev) => ({
                                                                            ...prev,
                                                                            volunteerWork: { ...prev.volunteerWork, startDate: date },
                                                                        }))
                                                                    }
                                                                    initialFocus
                                                                />
                                                            </PopoverContent>
                                                        </Popover>
                                                        <span className="text-sm text-gray-500 font-medium">to</span>
                                                        <Popover>
                                                            <PopoverTrigger asChild>
                                                                <Button
                                                                    variant="outline"
                                                                    className={cn(
                                                                        "flex-1 justify-start text-left font-normal h-12 border-2 border-gray-200 hover:border-green-400",
                                                                        !formData.volunteerWork.endDate && "text-muted-foreground",
                                                                    )}
                                                                >
                                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                                    {formData.volunteerWork.endDate
                                                                        ? format(formData.volunteerWork.endDate, "PPP")
                                                                        : "End Date"}
                                                                </Button>
                                                            </PopoverTrigger>
                                                            <PopoverContent className="w-auto p-0" align="start">
                                                                <Calendar
                                                                    mode="single"
                                                                    selected={formData.volunteerWork.endDate}
                                                                    onSelect={(date) =>
                                                                        setFormData((prev) => ({
                                                                            ...prev,
                                                                            volunteerWork: { ...prev.volunteerWork, endDate: date },
                                                                        }))
                                                                    }
                                                                    initialFocus
                                                                />
                                                            </PopoverContent>
                                                        </Popover>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </motion.div>

                                {/* Awards and Honors */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.4 }}
                                    className="space-y-6"
                                >
                                    <Card className="border-2 border-yellow-100 hover:border-yellow-200 transition-colors">
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center gap-3">
                                                <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                                                    <Award className="w-5 h-5 text-white" />
                                                </div>
                                                <div>
                                                    <h3 className="text-xl font-semibold text-gray-800">Awards and Honors Received</h3>
                                                    <p className="text-sm text-gray-500">Recognition for your achievements</p>
                                                </div>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-6">
                                            <div className="grid md:grid-cols-3 gap-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="awardName" className="text-sm font-semibold text-gray-700">
                                                        Name
                                                    </Label>
                                                    <Input
                                                        id="awardName"
                                                        value={newAward.name}
                                                        onChange={(e) => setNewAward((prev) => ({ ...prev, name: e.target.value }))}
                                                        placeholder="Scholarship of Excellence"
                                                        className="h-12 border-2 border-gray-200 focus:border-yellow-400 transition-colors"
                                                    />
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="awardReason" className="text-sm font-semibold text-gray-700">
                                                        Reason
                                                    </Label>
                                                    <Textarea
                                                        id="awardReason"
                                                        value={newAward.reason}
                                                        onChange={(e) => setNewAward((prev) => ({ ...prev, reason: e.target.value }))}
                                                        placeholder="e.g. Recognized for demonstrating outstanding academic performance and extracurricular involvement."
                                                        className="min-h-[48px] border-2 border-gray-200 focus:border-yellow-400 transition-colors resize-none"
                                                    />
                                                </div>

                                                <div className="space-y-2">
                                                    <Label className="text-sm font-semibold text-gray-700">Date</Label>
                                                    <Popover>
                                                        <PopoverTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                className={cn(
                                                                    "w-full justify-start text-left font-normal h-12 border-2 border-gray-200 hover:border-yellow-400",
                                                                    !newAward.date && "text-muted-foreground",
                                                                )}
                                                            >
                                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                                {newAward.date ? format(newAward.date, "PPP") : "Select date"}
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent className="w-auto p-0" align="start">
                                                            <Calendar
                                                                mode="single"
                                                                selected={newAward.date}
                                                                onSelect={(date) => setNewAward((prev) => ({ ...prev, date }))}
                                                                initialFocus
                                                            />
                                                        </PopoverContent>
                                                    </Popover>
                                                </div>
                                            </div>

                                            <Button
                                                type="button"
                                                onClick={addAward}
                                                className="w-full h-12 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
                                            >
                                                <Plus className="w-4 h-4 mr-2" />
                                                Add Award
                                            </Button>

                                            <AnimatePresence>
                                                {formData.awards.map((award, index) => (
                                                    <motion.div
                                                        key={award.id}
                                                        initial={{ opacity: 0, y: -10 }}
                                                        animate={{ opacity: 1, y: 0 }}
                                                        exit={{ opacity: 0, y: -10 }}
                                                        transition={{ delay: index * 0.1 }}
                                                        className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-xl border border-yellow-100"
                                                    >
                                                        <div className="flex items-start justify-between">
                                                            <div className="flex-1">
                                                                <div className="flex items-center gap-3 mb-2">
                                                                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                                                        <Award className="w-4 h-4 text-white" />
                                                                    </div>
                                                                    <h4 className="font-semibold text-gray-800">{award.name}</h4>
                                                                </div>
                                                                <p className="text-sm text-gray-600 mb-2 leading-relaxed">{award.reason}</p>
                                                                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                                                                    {award.date ? format(award.date, "PPP") : "No date"}
                                                                </Badge>
                                                            </div>
                                                            <Button
                                                                type="button"
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => removeAward(award.id)}
                                                                className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                                            >
                                                                <X className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </motion.div>
                                                ))}
                                            </AnimatePresence>
                                        </CardContent>
                                    </Card>
                                </motion.div>

                                {/* Study Abroad Question */}
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.5 }}
                                    className="space-y-6"
                                >
                                    <Card className="border-2 border-indigo-100 hover:border-indigo-200 transition-colors">
                                        <CardContent className="p-6">
                                            <div className="space-y-4">
                                                <Label className="text-lg font-semibold text-gray-800">
                                                    Have you traveled or studied abroad before?
                                                </Label>
                                                <div className="flex gap-6">
                                                    <label className="flex items-center space-x-3 cursor-pointer">
                                                        <input
                                                            type="radio"
                                                            name="studiedAbroad"
                                                            value="Yes"
                                                            checked={formData.hasStudiedAbroad === "Yes"}
                                                            onChange={(e) => setFormData((prev) => ({ ...prev, hasStudiedAbroad: e.target.value }))}
                                                            className="w-4 h-4 text-indigo-600 border-2 border-gray-300 focus:ring-indigo-500"
                                                        />
                                                        <span className="text-sm font-medium text-gray-700">Yes</span>
                                                    </label>
                                                    <label className="flex items-center space-x-3 cursor-pointer">
                                                        <input
                                                            type="radio"
                                                            name="studiedAbroad"
                                                            value="No"
                                                            checked={formData.hasStudiedAbroad === "No"}
                                                            onChange={(e) => setFormData((prev) => ({ ...prev, hasStudiedAbroad: e.target.value }))}
                                                            className="w-4 h-4 text-indigo-600 border-2 border-gray-300 focus:ring-indigo-500"
                                                        />
                                                        <span className="text-sm font-medium text-gray-700">No</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </motion.div>

                                {/* Navigation Buttons */}
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.6 }}
                                    className="flex gap-4 pt-8"
                                >
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handlePrevious}
                                        className="flex-1 h-14 text-gray-600 border-2 border-gray-300 hover:bg-gray-50 font-medium"
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex-1 h-14 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium text-lg relative overflow-hidden"
                                    >
                                        {isLoading ? (
                                            <div className="flex items-center gap-2">
                                                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                                Saving...
                                            </div>
                                        ) : (
                                            "Save & Continue"
                                        )}
                                    </Button>
                                </motion.div>
                            </form>
                        </CardContent>
                    </Card>
                </motion.div>

                {/* Success Animation */}
                <AnimatePresence>
                    {showSuccess && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            className="fixed inset-0 flex items-center justify-center z-50 bg-black/20 backdrop-blur-sm"
                        >
                            <motion.div
                                initial={{ y: 50 }}
                                animate={{ y: 0 }}
                                className="bg-white rounded-2xl p-8 shadow-2xl flex flex-col items-center"
                            >
                                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mb-4">
                                    <CheckCircle2 className="w-8 h-8 text-white" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-800 mb-2">Form Saved Successfully!</h3>
                                <p className="text-gray-600 text-center">Your extracurricular activities have been recorded.</p>
                            </motion.div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </div>
    )
}
