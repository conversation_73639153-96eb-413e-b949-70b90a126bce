"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname, useRouter } from "next/navigation"
import {
    BarChart3,
    BookOpen,
    Briefcase,
    Building2,
    Calendar,
    CheckCircle,
    ChevronDown,
    Circle,
    DollarSign,
    FileText,
    GraduationCap,
    Heart,
    HelpCircle,
    Home,
    Info,
    LayoutDashboard,
    LineChart,
    LogOut,
    Mail,
    MessageSquare,
    StampIcon as Passport,
    Plane,
    School,
    Settings,
    Trophy,
    User,
    Users,
    Video,
    Wallet,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"

export function StudentSidebar() {
    const pathname = usePathname()
    const router = useRouter()
    const [expandedSections, setExpandedSections] = useState({
        main: true,
        application: true,
        services: false,
        account: false,
        support: false,
    })
    const [formCompletionStatus, setFormCompletionStatus] = useState<Record<string, boolean>>({})
    const [userData, setUserData] = useState<{ applicationType?: string } | null>(null)

    useEffect(() => {
        // Load user data from localStorage
        const userDataStr = localStorage.getItem("user-data")
        if (userDataStr) {
            setUserData(JSON.parse(userDataStr))
        }

        const checkFormCompletion = () => {
            const personalInfo = localStorage.getItem("personalInfo_completed")
            const guardianInfo = localStorage.getItem("guardianInfo_completed")
            const academicBackground = localStorage.getItem("academicBackground_completed")
            const programPreference = localStorage.getItem("programPreference_completed")
            const financialInfo = localStorage.getItem("financialInfo_completed")
            const extracurricularActivities = localStorage.getItem("extracurricularActivities_completed")
            const workExperience = localStorage.getItem("workExperience_completed")
            const healthInfo = localStorage.getItem("healthInfo_completed")
            const additionalInfo = localStorage.getItem("additionalInfo_completed")
            const fundingHub = localStorage.getItem("fundingHub_completed")
            const athleteInfo = localStorage.getItem("athleteInfo_completed")

            setFormCompletionStatus({
                personalInfo: personalInfo === "true",
                guardianInfo: guardianInfo === "true",
                academicBackground: academicBackground === "true",
                programPreference: programPreference === "true",
                financialInfo: financialInfo === "true",
                extracurricularActivities: extracurricularActivities === "true",
                workExperience: workExperience === "true",
                healthInfo: healthInfo === "true",
                additionalInfo: additionalInfo === "true",
                fundingHub: fundingHub === "true",
                athleteInfo: athleteInfo === "true",
            })
        }

        checkFormCompletion()

        // Listen for storage changes to update status in real-time
        window.addEventListener("storage", checkFormCompletion)

        // Listen for custom form status update events
        window.addEventListener("formStatusUpdate", checkFormCompletion)

        return () => {
            window.removeEventListener("storage", checkFormCompletion)
            window.removeEventListener("formStatusUpdate", checkFormCompletion)
        }
    }, [])

    const toggleSection = (section: string) => {
        setExpandedSections((prev) => ({
            ...prev,
            [section]: !prev[section as keyof typeof prev],
        }))
    }

    const getStepIcon = (formKey: keyof typeof formCompletionStatus, href: string) => {
        // If form is completed, show checkmark
        if (formCompletionStatus[formKey]) {
            return <CheckCircle className="h-4 w-4 text-green-600" />
        }
        // If it's the current page, show filled circle
        if (pathname === href) {
            return <Circle className="h-4 w-4 text-blue-600 fill-blue-600" />
        }
        // Otherwise show empty circle
        return <Circle className="h-4 w-4 text-gray-400" />
    }

    const getStepTextColor = (formKey: keyof typeof formCompletionStatus, href: string) => {
        if (formCompletionStatus[formKey]) {
            return "text-green-700"
        }
        if (pathname === href) {
            return "text-blue-700"
        }
        return "text-gray-700"
    }

    // Filter application form items based on user's application type
    const getApplicationFormItems = () => {
        const baseItems = [
            {
                title: "Personal Info",
                href: "/student/application-form/personal-info",
                icon: User,
                formKey: "personalInfo" as keyof typeof formCompletionStatus,
            },
            {
                title: "Guardian Info",
                href: "/student/application-form/guardian-info",
                icon: Users,
                formKey: "guardianInfo" as keyof typeof formCompletionStatus,
            },
            {
                title: "Academic Background",
                href: "/student/application-form/academic-background",
                icon: School,
                formKey:
                    "academicBackground" as keyof typeof formCompletionStatus,
            },
            {
                title: "Program Preference",
                href: "/student/application-form/program-preference",
                icon: GraduationCap,
                formKey:
                    "programPreference" as keyof typeof formCompletionStatus,
            },
            {
                title: "Financial Info",
                href: "/student/application-form/financial-info",
                icon: DollarSign,
                formKey: "financialInfo" as keyof typeof formCompletionStatus,
            },
            {
                title: "Extracurricular Activities",
                href: "/student/application-form/extracurricular-activities",
                icon: Trophy,
                formKey:
                    "extracurricularActivities" as keyof typeof formCompletionStatus,
            },
            {
                title: "Work Experience",
                href: "/student/application-form/work-experience",
                icon: Briefcase,
                formKey: "workExperience" as keyof typeof formCompletionStatus,
            },
            {
                title: "Health Info",
                href: "/student/application-form/health-info",
                icon: Heart,
                formKey: "healthInfo" as keyof typeof formCompletionStatus,
            },
        ];

        // Add the final step based on application type
        if (userData?.applicationType === "athlete") {
            // For athletes, athlete-info is the last step and handles submission
            baseItems.push({
                title: "Athlete Info",
                href: "/student/application-form/athlete-info",
                icon: School,
                formKey: "athleteInfo" as keyof typeof formCompletionStatus,
            });
        } else if (
            userData?.applicationType === "masters" ||
            userData?.applicationType === "phd"
        ) {
            // For masters/phd, funding-hub is the last step and handles submission
            baseItems.push({
                title: "Scholarship & Funding",
                href: "/student/application-form/funding-hub",
                icon: School,
                formKey:
                    "fundingHub" as keyof typeof formCompletionStatus,
            });
        } else {
            // For all other types, additional-info is the last step and handles submission
            baseItems.push({
                title: "Additional Info",
                href: "/student/application-form/additional-info",
                icon: Info,
                formKey: "additionalInfo" as keyof typeof formCompletionStatus,
            });
        }

        return baseItems;
    }

    const menuGroups = [
        {
            id: "main",
            title: "Main",
            items: [
                {
                    title: "Dashboard",
                    href: "/student",
                    icon: LayoutDashboard,
                    exact: true,
                },
                {
                    title: "Transcript",
                    href: "/student/transcript",
                    icon: GraduationCap,
                },
                // {
                //     title: "Application Form",
                //     href: "/student/application-form",
                //     icon: FileText,
                //     badge: "3",
                // },
                {
                    title: "School Reference",
                    href: "/student/school-reference",
                    icon: Building2,
                },
                {
                    title: "Track Applications",
                    href: "/student/track-applications",
                    icon: LineChart,
                },
            ],
        },
        {
            id: "application",
            title: "Application Form",
            items: getApplicationFormItems(),
        },
        {
            id: "services",
            title: "Services",
            items: [
                {
                    title: "Payment",
                    href: "/student/payment",
                    icon: DollarSign,
                },
                {
                    title: "Scholarships",
                    href: "/student/scholarships",
                    icon: BookOpen,
                },
                {
                    title: "Visa Corner",
                    href: "/student/visa-corner",
                    icon: Passport,
                },
                {
                    title: "Accommodation",
                    href: "/student/accommodation",
                    icon: Home,
                    badge: "New",
                },
                {
                    title: "Travel Hub",
                    href: "/student/travel-hub",
                    icon: Plane,
                },
                {
                    title: "Financial Advice",
                    href: "/student/financial-advice",
                    icon: Wallet,
                },
            ],
        },
        {
            id: "account",
            title: "Account",
            items: [
                {
                    title: "Profile",
                    href: "/student/profile",
                    icon: User,
                },
                {
                    title: "Analytics",
                    href: "/student/analytics",
                    icon: BarChart3,
                },
                {
                    title: "Settings",
                    href: "/student/settings",
                    icon: Settings,
                },
            ],
        },
        {
            id: "support",
            title: "Support",
            items: [
                {
                    title: "Help Center",
                    href: "/student/help",
                    icon: HelpCircle,
                },
                {
                    title: "Messages",
                    href: "/student/messages",
                    icon: MessageSquare,
                    badge: "5",
                },
                {
                    title: "Video Tutorials",
                    href: "/student/video-tutorials",
                    icon: Video,
                },
                {
                    title: "Contact Us",
                    href: "/student/contact",
                    icon: Mail,
                },
            ],
        },
    ]

    const isActive = (href: string, exact = false) => {
        if (exact) {
            return pathname === href
        }
        return pathname === href || pathname.startsWith(`${href}/`)
    }

    const deleteCookie = (name: string) => {
        document.cookie = `${name}=; Max-Age=0; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    };

    const handleLogout = () => {
        deleteCookie("auth-token");
        deleteCookie("user-type");
        router.push("/")
    };

    return (
        <aside className="fixed inset-y-0 left-0 z-20 hidden w-64 flex-shrink-0 border-r border-gray-200 bg-white md:flex md:flex-col">
            <div className="flex h-16 items-center justify-between border-b border-gray-200 px-4">
                <Link href="/student" className="flex items-center gap-2">
                    <div className="relative h-8 w-8 overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-blue-500">
                        <Image src="/placeholder.svg" alt="CLA 360 Logo" width={32} height={32} className="h-8 w-8 object-cover" />
                    </div>
                    <span className="text-lg font-bold text-gray-900">CLA 360</span>
                </Link>
            </div>

            <ScrollArea className="flex-1 px-3 py-4">
                <div className="space-y-4">
                    {menuGroups.map((section) => (
                        <div key={section.id} className="space-y-1">
                            {/* Section Header - Clickable to toggle */}
                            <div
                                className="flex cursor-pointer items-center justify-between rounded-md px-4 py-2 hover:bg-gray-100"
                                onClick={() => toggleSection(section.id)}
                            >
                                <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500">{section.title}</h3>
                                <ChevronDown
                                    className={cn(
                                        "h-4 w-4 text-gray-500 transition-transform duration-200",
                                        expandedSections[section.id as keyof typeof expandedSections] ? "rotate-180" : "",
                                    )}
                                />
                            </div>

                            {/* Section Content - Conditionally rendered based on expanded state */}
                            {expandedSections[section.id as keyof typeof expandedSections] && (
                                <ul className="space-y-1 pl-2">
                                    {section.items.map((item, j) => (
                                        <li key={j}>
                                            <Link
                                                href={item.href}
                                                className={cn(
                                                    "group flex items-center justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors",
                                                    isActive(item.href, (item as any).exact)
                                                        ? "bg-purple-50 text-purple-700"
                                                        : "text-gray-700 hover:bg-gray-100 hover:text-gray-900",
                                                )}
                                            >
                                                <div className="flex items-center gap-3">
                                                    {/* Show step status icon for application form items */}
                                                    {(item as any).formKey ? (
                                                        getStepIcon((item as any).formKey, item.href)
                                                    ) : (
                                                        <item.icon
                                                            className={cn(
                                                                "h-5 w-5",
                                                                isActive(item.href, (item as any).exact)
                                                                    ? "text-purple-700"
                                                                    : "text-gray-500 group-hover:text-gray-700",
                                                            )}
                                                        />
                                                    )}
                                                    <span className={(item as any)?.formKey ? getStepTextColor((item as any)?.formKey, item.href) : ""}>
                                                        {item.title}
                                                    </span>
                                                </div>
                                                {(item as any).badge && (
                                                    <span
                                                        className={cn(
                                                            "rounded-full px-2 py-0.5 text-xs font-semibold",
                                                            (item as any).badge === "New" ? "bg-blue-100 text-blue-700" : "bg-purple-100 text-purple-700",
                                                        )}
                                                    >
                                                        {(item as any).badge}
                                                    </span>
                                                )}
                                            </Link>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    ))}
                </div>
            </ScrollArea>

            <div className="border-t border-gray-200 p-4">
                <div className="flex items-center gap-3">
                    <div className="relative h-10 w-10 overflow-hidden rounded-full">
                        <Image src="/placeholder.svg" alt="User" width={40} height={40} className="h-10 w-10 object-cover" />
                    </div>
                    <div className="flex-1 truncate">
                        <p className="text-sm font-medium text-gray-900">Alex Johnson</p>
                        <p className="truncate text-xs text-gray-500">CLA-8220438</p>
                    </div>
                    <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
                        <LogOut onClick={handleLogout} className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </aside>
    )
}
