"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

export default function Login() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [userType, setUserType] = useState<"student" | "admin">("student")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
console.log("we here");

    // In a real app, you would validate credentials against a backend
    // For now, we'll use dummy logic to redirect based on userType
    if (email && password) {
      if (userType === "admin") {
        router.push("/admin")
      } else {
        router.push("/student")
      }
    }
  }

  // For demo purposes, let's add a quick way to toggle between user types
  const toggleUserType = () => {
    setUserType(userType === "student" ? "admin" : "student")
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
      <div className="mb-8 flex items-center gap-2">
        <Image
          src="/placeholder.svg?height=50&width=50"
          alt="CLA 360 Logo"
          width={50}
          height={50}
          className="h-12 w-12"
        />
        <span className="text-2xl font-bold text-purple-800">College League App</span>
      </div>

      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center text-2xl">Login</CardTitle>
          <CardDescription className="text-center">Enter your credentials to access your account</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Input
                  id="userType"
                  type="checkbox"
                  className="h-4 w-4"
                  checked={userType === "admin"}
                  onChange={toggleUserType}
                />
                <Label htmlFor="userType" className="text-sm font-normal">
                  Login as {userType === "admin" ? "Admin" : "Student"}
                </Label>
              </div>
              <Link href="#" className="text-sm text-purple-700 hover:text-purple-500">
                Forgot password?
              </Link>
            </div>

            <Button type="submit" className="w-full bg-blue-500 hover:bg-blue-600">
              Login
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <div className="text-center text-sm">
            Don&apos;t have an account?{" "}
            <Link href="#" className="text-purple-700 hover:text-purple-500">
              Sign up
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
